import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

/**
 * Handles Google OAuth callback
 * Verifies token, creates/returns JWT and refresh token
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const code = searchParams.get('code')
  const state = searchParams.get('state')
  const error = searchParams.get('error')

  // Check for errors from Google
  if (error) {
    console.error('Google OAuth error:', error)
    return NextResponse.redirect(`${request.nextUrl.origin}/student?error=google_auth_failed`)
  }

  // Validate required parameters
  if (!code || !state) {
    console.error('Missing required parameters:', { code: !!code, state: !!state })
    return NextResponse.redirect(`${request.nextUrl.origin}/student?error=invalid_request`)
  }

  // Validate state parameter to prevent CSRF attacks
  

  // Get state from cookie as a backup
  const cookieState = request.cookies.get('oauth_state')?.value
  

  try {
    // Try to get state from cache first
    const storedState = await cache.get(`auth:state:${state}`)
    

    // Validate state from cache or cookie
    const isValidState = storedState === 'true' || (cookieState && cookieState === state)

    if (!isValidState) {
      console.error(
        `State validation failed. Cache state: ${storedState}, Cookie state: ${cookieState}, Received state: ${state}`
      )

      // Note: We can't easily list all keys in Redis from our interface
      // Let's just log that the state validation failed
      

      return NextResponse.redirect(`${request.nextUrl.origin}/student?error=invalid_state`)
    }

    // Clean up the state from cache
    await cache.del(`auth:state:${state}`)
    
  } catch (error) {
    console.error('Error validating state parameter:', error)
    return NextResponse.redirect(`${request.nextUrl.origin}/student?error=cache_error`)
  }

  try {
    // Exchange the authorization code for tokens
    const googleClientId = serverConfig.auth.googleClientId
    const googleClientSecret = serverConfig.auth.googleClientSecret
    const redirectUri = `${request.nextUrl.origin}/api/auth/student/google/callback`

    

    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        code,
        client_id: googleClientId || '',
        client_secret: googleClientSecret || '',
        redirect_uri: redirectUri,
        grant_type: 'authorization_code',
      }),
    })

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json()
      console.error('Google token exchange error:', errorData)
      return NextResponse.redirect(`${request.nextUrl.origin}/student?error=token_exchange_failed`)
    }

    const tokenData = await tokenResponse.json()
    

    // Get user info from Google
    const userInfoResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: { Authorization: `Bearer ${tokenData.access_token}` },
    })

    if (!userInfoResponse.ok) {
      console.error('Failed to get Google user info:', await userInfoResponse.text())
      return NextResponse.redirect(`${request.nextUrl.origin}/student?error=user_info_failed`)
    }

    const userData = await userInfoResponse.json()
    

    // Register or login the student with Google
    const { token, refreshToken, student } = await authUseCases.registerWithGoogle(
      userData.email,
      userData.name
    )

    // Set the tokens as cookies
    const response = NextResponse.redirect(
      student.whatsapp
        ? `${request.nextUrl.origin}/student/home`
        : `${request.nextUrl.origin}/student/profile?prompt=whatsapp`
    )

    // Set HTTP-only cookies for security
    response.cookies.set('student_auth_token', token, {
      httpOnly: true,
      secure: serverConfig.environment.isProduction,
      sameSite: 'lax',
      maxAge: 60 * 60, // 1 hour
      path: '/',
    })

    response.cookies.set('student_refresh_token', refreshToken, {
      httpOnly: true,
      secure: serverConfig.environment.isProduction,
      sameSite: 'lax',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: '/',
    })

    // Clear the state cookie
    response.cookies.set('oauth_state', '', {
      httpOnly: true,
      secure: serverConfig.environment.isProduction,
      sameSite: 'lax',
      maxAge: 0, // Expire immediately
      path: '/',
    })

    
    return response
  } catch (error) {
    console.error('Google callback error:', error)
    return NextResponse.redirect(`${request.nextUrl.origin}/student?error=server_error`)
  }
}
