import { NextRequest, NextResponse } from 'next/server'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

/**
 * Initiates Google OAuth flow
 * Redirects to Google consent screen
 */
export async function GET(request: NextRequest) {
  

  const googleClientId = serverConfig.auth.googleClientId
  const googleClientSecret = serverConfig.auth.googleClientSecret

  if (!googleClientId || !googleClientSecret) {
    console.error('Google OAuth credentials not configured')
    return NextResponse.json({ error: 'Google OAuth credentials not configured' }, { status: 500 })
  }

  // Generate a random state parameter to prevent CSRF attacks
  const state = Math.random().toString(36).substring(2, 15)
  

  // Store the state in the cache for validation in the callback
  try {
    await cache.set(`auth:state:${state}`, 'true', 30 * 60) // 30 minutes TTL
    

    // Verify that the state was stored correctly
    const storedState = await cache.get(`auth:state:${state}`)
    

    if (!storedState) {
      console.error('Failed to store state in cache')
      return NextResponse.json(
        { error: 'Failed to initialize authentication flow - cache error' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error storing state parameter in cache:', error)
    return NextResponse.json({ error: 'Failed to initialize authentication flow' }, { status: 500 })
  }

  // Construct the Google OAuth URL
  const redirectUri = `${request.nextUrl.origin}/api/auth/student/google/callback`
  

  const scope = encodeURIComponent('openid email profile')

  const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${googleClientId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}&state=${state}`
  

  // Create response with redirect
  const response = NextResponse.redirect(googleAuthUrl)

  // Store the state in a cookie for additional verification
  response.cookies.set('oauth_state', state, {
    httpOnly: true,
    secure: serverConfig.environment.isProduction,
    sameSite: 'lax',
    maxAge: 60 * 30, // 30 minutes
    path: '/',
    domain: process.env.NODE_ENV === 'production' ? serverConfig.environment.domain : undefined,
  })

  return response
}
