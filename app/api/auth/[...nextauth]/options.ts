import { NextAuthOptions } from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { AuthUseCases } from '@/lib/domain/usecases/auth'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'

// Initialize dependencies
const cache = getRedisCache()
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const authUseCases = new AuthUseCases(
  studentRepo,
  adminRepo,
  cache,
  serverConfig.auth.jwtSecret || ''
)

// Explicitly set for development to use localhost
const isDevelopment = process.env.NODE_ENV !== 'production'
const baseUrl = isDevelopment ? 'http://localhost:3000' : serverConfig.auth.nextAuthUrl

// Export configuration as the default export
const options: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: serverConfig.auth.googleClientId || '',
      clientSecret: serverConfig.auth.googleClientSecret || '',
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (!user.email) {
        console.error('No email provided by Google')
        return false
      }

      try {
        // Register or login the student with Google
        const { student } = await authUseCases.registerWithGoogle(
          user.email,
          user.name || 'Student User'
        )

        // Store the database ID in the user object for later use in the JWT callback
        // @ts-ignore - Add custom properties to user
        user.dbId = student.id

        return true
      } catch (error) {
        console.error('Error in NextAuth signIn callback:', error)
        return false
      }
    },
    async jwt({ token, user }) {
      // Add user data to the token
      if (user) {
        token.role = 'student'
        // Store the database ID in the token
        // @ts-ignore - Add custom properties to token
        token.dbId = user.dbId
      }
      return token
    },
    async session({ session, token }) {
      // Add user info to the session
      if (session.user) {
        // @ts-ignore - Adding custom properties
        session.user.role = 'student'
        // Use our database ID instead of Google's ID
        // @ts-ignore - Adding custom properties
        session.user.id = token.dbId || token.sub
      }
      return session
    },
  },
  pages: {
    signIn: '/student',
    error: '/student?error=auth',
    signOut: '/student',
  },
  session: {
    strategy: 'jwt',
    maxAge: 60 * 60, // 1 hour
  },
  cookies: {
    // Use a different cookie name to avoid conflicts with admin auth
    sessionToken: {
      name: 'student_session',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: !isDevelopment,
        domain: undefined, // No domain restriction for dev
      },
    },
  },
  debug: isDevelopment,
  // @ts-ignore - trustHost exists in NextAuth but TypeScript definition might be outdated
  trustHost: true,

  // Force the URL setting to ensure proper redirects
  // @ts-ignore - url may be outdated in type definition
  url: baseUrl,
}

export default options
