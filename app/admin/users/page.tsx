'use client'

import type React from 'react'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { useToast } from '@/components/ui/use-toast'
import {
  Edit,
  Plus,
  Trash2,
  Loader2,
  AlertCircle,
  Shield,
  ShieldCheck,
  User2,
  Search,
  X,
  Upload,
  Download,
  Trash,
  SwitchCamera,
  XCircle,
} from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAdminSession } from '@/hooks/use-admin-session'
import { useAdminPermission } from '@/hooks/use-admin-permission'
import { useRouter } from 'next/navigation'
import Papa from 'papaparse'
import { useQrDownload, QrDownloadDialog, QrDownloadButton } from '@/features/bulk-qr-download'

// User type definition
interface User {
  id: number
  name: string
  role: 'student' | 'admin' | 'super_admin'
  uniqueCode?: string
  username?: string
  classId?: number
  className?: string
  hasAttendanceRecords?: boolean
}

// Form data type with explicit string type for role to avoid TS errors
interface FormData {
  id: string
  name: string
  role: string
  password: string
  classId: string
}

// Interface for form validation errors
interface FormErrors {
  password?: string
}

// Define the dialog mode type explicitly
type DialogMode = 'add' | 'edit'

export default function AdminUsers() {
  const { toast } = useToast()
  const { admin } = useAdminSession()
  const { permission, loading: permissionLoading } = useAdminPermission('super_admin')
  const router = useRouter()
  const [showDialog, setShowDialog] = useState(false)
  const [dialogMode, setDialogMode] = useState<DialogMode>('add')
  const [selectedUser, setSelectedUser] = useState<null | User>(null)
  const [formData, setFormData] = useState<FormData>({
    id: '',
    name: '',
    role: 'student',
    password: '',
    classId: '',
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [showPasswordAlert, setShowPasswordAlert] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingClasses, setIsLoadingClasses] = useState(false)
  const [isCheckingAttendance, setIsCheckingAttendance] = useState(false)
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [paginatedUsers, setPaginatedUsers] = useState<User[]>([])
  const [classes, setClasses] = useState<{ id: number; name: string }[]>([])
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [apiStatus, setApiStatus] = useState<string | null>(null)
  const [deleteError, setDeleteError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  // roleFilter removed since this page is for students only
  const [classFilter, setClassFilter] = useState<string>('all') // Add class filter state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [showBulkUploadDialog, setShowBulkUploadDialog] = useState(false)
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState<{
    success: number
    failed: number
    total: number
  } | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadErrors, setUploadErrors] = useState<
    Array<{ row: number; message: string; data: any }>
  >([])
  const [showUploadErrors, setShowUploadErrors] = useState(false)
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set())
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [deleteAttendanceRecords, setDeleteAttendanceRecords] = useState(true)
  const [deleteProgress, setDeleteProgress] = useState<{
    success: number
    failed: number
    total: number
  } | null>(null)
  const [deleteErrors, setDeleteErrors] = useState<Array<{ userId: number; message: string }>>([])
  const [showDeleteErrors, setShowDeleteErrors] = useState(false)
  const [csvPreviewData, setCsvPreviewData] = useState<Array<any>>([])
  const [csvValidationErrors, setCsvValidationErrors] = useState<
    Array<{ row: number; message: string; data: any }>
  >([])
  const [showCsvValidationErrors, setShowCsvValidationErrors] = useState(false)
  const [uploadStep, setUploadStep] = useState<'select' | 'validate' | 'upload'>('select')
  const [isValidating, setIsValidating] = useState(false)
  const [totalValidRecords, setTotalValidRecords] = useState(0)
  const [sortConfig, setSortConfig] = useState<{
    key: string
    direction: 'ascending' | 'descending'
  } | null>(null)
  const [showBulkUpdateClassDialog, setShowBulkUpdateClassDialog] = useState(false)
  const [sourceClassId, setSourceClassId] = useState<string>('')
  const [targetClassId, setTargetClassId] = useState<string>('')
  const [isUpdatingClass, setIsUpdatingClass] = useState(false)
  const [updateClassProgress, setUpdateClassProgress] = useState<{
    success: number
    failed: number
    total: number
  } | null>(null)
  const [updateClassErrors, setUpdateClassErrors] = useState<
    Array<{ userId: number; name: string; message: string }>
  >([])
  const [showUpdateClassErrors, setShowUpdateClassErrors] = useState(false)

  // QR Download feature hook
  const qrDownload = useQrDownload(users)

  // Function to perform natural sorting (correct sorting for strings with numbers)
  const naturalSort = (a: string, b: string): number => {
    // Regular expression to split strings into text and numeric parts
    const pattern = /(\d+|\D+)/g

    // Extract parts from strings
    const partsA = String(a).match(pattern) || []
    const partsB = String(b).match(pattern) || []

    // Compare each part
    for (let i = 0; i < Math.min(partsA.length, partsB.length); i++) {
      const partA = partsA[i]
      const partB = partsB[i]

      // Check if both parts are numeric
      const numA = !isNaN(Number(partA))
      const numB = !isNaN(Number(partB))

      if (numA && numB) {
        // If both parts are numbers, compare as numbers
        const diff = parseInt(partA) - parseInt(partB)
        if (diff !== 0) return diff
      } else {
        // Otherwise, compare as strings
        const diff = partA.localeCompare(partB)
        if (diff !== 0) return diff
      }
    }

    // If all parts compared so far are equal, compare by length
    return partsA.length - partsB.length
  }

  // Check API endpoint availability
  useEffect(() => {
    const checkApiEndpoint = async () => {
      try {
        const response = await fetch('/api/users', { method: 'HEAD' })
        if (!response.ok) {
          console.warn('API endpoint /api/users may not be implemented correctly')
          setApiStatus('API endpoint may not be available')
        } else {
          setApiStatus(null)
        }
      } catch (error) {
        console.error('Error checking API endpoint:', error)
        setApiStatus('API endpoint is not responding')
      }
    }

    checkApiEndpoint()
  }, [])

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/users')

      if (!response.ok) {
        throw new Error('Failed to fetch users')
      }

      const data = await response.json()
      // Filter to show only students
      const studentUsers = data.filter((user: any) => user.role === 'student')
      setUsers(studentUsers)
      return studentUsers
    } catch (error) {
      console.error('Error fetching users:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengambil data pengguna',
        variant: 'destructive',
      })
      return []
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch classes from API
  const fetchClasses = async () => {
    try {
      setIsLoadingClasses(true)
      const response = await fetch('/api/classes')

      if (!response.ok) {
        throw new Error('Failed to fetch classes')
      }

      const data = await response.json()
      setClasses(data)
      return data
    } catch (error) {
      console.error('Error fetching classes:', error)
      return []
    } finally {
      setIsLoadingClasses(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    async function loadData() {
      await fetchClasses()
      await fetchUsers()
    }
    loadData()
  }, [])

  // Filter users whenever search query, class filter, or users array changes
  useEffect(() => {
    let result = [...users]

    // Since this page is for students only, no role filtering needed
    // All users in this page are already filtered to be students

    // Filter by class if not 'all'
    if (classFilter !== 'all') {
      const classIdNum = parseInt(classFilter, 10)
      result = result.filter(user => user.classId === classIdNum)
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        user =>
          user.name.toLowerCase().includes(query) ||
          (user.username && user.username.toLowerCase().includes(query)) ||
          (user.uniqueCode && user.uniqueCode.toLowerCase().includes(query)) ||
          (user.className && user.className.toLowerCase().includes(query))
      )
    }

    // Apply sorting if configured
    if (sortConfig !== null) {
      result.sort((a, b) => {
        // Handle different field types appropriately
        let valueA: any
        let valueB: any

        // Special handling for the ID/Unique Code column
        if (sortConfig.key === 'identifier') {
          valueA = a.uniqueCode || a.username || ''
          valueB = b.uniqueCode || b.username || ''
        } else {
          valueA = a[sortConfig.key as keyof User]
          valueB = b[sortConfig.key as keyof User]
        }

        // Handle nullish values for sorting
        if (valueA === null || valueA === undefined) valueA = ''
        if (valueB === null || valueB === undefined) valueB = ''

        // Convert to strings for natural sort
        valueA = String(valueA).toLowerCase()
        valueB = String(valueB).toLowerCase()

        // Apply natural sort for string values
        const comparison = naturalSort(valueA, valueB)

        // Apply sort direction
        return sortConfig.direction === 'ascending' ? comparison : -comparison
      })
    }

    setFilteredUsers(result)
    // Reset to first page when filters change
    setCurrentPage(1)
  }, [users, searchQuery, classFilter, sortConfig])

  // Update paginated users when filtered users or pagination settings change
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    setPaginatedUsers(filteredUsers.slice(startIndex, endIndex))
  }, [filteredUsers, currentPage, itemsPerPage])

  const handleAddUser = () => {
    setDialogMode('add')
    setFormData({
      id: '',
      name: '',
      role: 'student',
      password: '',
      classId: '',
    })
    setShowDialog(true)
  }

  const handleBulkUpload = () => {
    setCsvFile(null)
    setUploadProgress(null)
    setUploadErrors([])
    setShowUploadErrors(false)
    setShowBulkUploadDialog(true)
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      if (file.name.endsWith('.csv')) {
        setCsvFile(file)
        // Reset validation state when a new file is selected
        setCsvPreviewData([])
        setCsvValidationErrors([])
        setShowCsvValidationErrors(false)
        setUploadStep('select')
      } else {
        toast({
          title: 'Error',
          description: 'File harus berformat CSV',
          variant: 'destructive',
        })
      }
    }
  }

  const downloadSampleCsv = () => {
    const headers = ['name', 'username', 'password', 'className', 'nis', 'googleEmail', 'whatsapp']
    const sampleData = [
      [
        'Siswa Example 1',
        'siswa1',
        'password123',
        'X IPA 1',
        '12345',
        '<EMAIL>',
        '081234567890',
      ],
      ['Siswa Example 2', 'siswa2', 'password123', 'X IPA 1', '12346', '', ''],
      ['Siswa Example 3', 'siswa3', 'password123', 'X IPA 2', '12347', '', ''],
    ]

    const csvContent = [headers.join(','), ...sampleData.map(row => row.join(','))].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', 'sample_students.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const validateCsvFile = async () => {
    if (!csvFile) return

    try {
      setIsValidating(true)
      setCsvPreviewData([])
      setCsvValidationErrors([])

      // Read and parse the CSV file
      const fileContent = await csvFile.text()
      const parser = new DOMParser()
      const csvDoc = parser.parseFromString(`<csv>${fileContent}</csv>`, 'text/xml')

      // Use Papa Parse for parsing CSV
      const parsedData = Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
      })

      if (parsedData.errors.length > 0) {
        setCsvValidationErrors(
          parsedData.errors.map((error: Papa.ParseError, index: number) => ({
            row: typeof error.row === 'number' ? error.row + 1 : index + 1, // Convert to 1-indexed for display
            message: `CSV parsing error: ${error.message}`,
            data: error,
          }))
        )
        setShowCsvValidationErrors(true)
        return
      }

      const records = parsedData.data

      if (records.length === 0) {
        toast({
          title: 'Error',
          description: 'CSV tidak berisi data',
          variant: 'destructive',
        })
        return
      }

      // Preview the first few records (up to 5)
      const previewData = records.slice(0, 5)
      setCsvPreviewData(previewData)

      // Validate all records against our schema
      const validationErrors: Array<{ row: number; message: string; data: any }> = []
      let validRecords = 0

      // Define validation schema (simplified version of the server-side schema)
      const requiredFields = ['name', 'username', 'password', 'className']

      records.forEach((record: any, index: number) => {
        const rowNumber = index + 2 // Account for header row + 1-indexed

        // Basic validation
        let isValid = true
        const missingFields = []

        // Check for required fields
        for (const field of requiredFields) {
          if (!record[field] || record[field].trim() === '') {
            isValid = false
            missingFields.push(field)
          }
        }

        if (missingFields.length > 0) {
          validationErrors.push({
            row: rowNumber,
            message: `Missing required fields: ${missingFields.join(', ')}`,
            data: record,
          })
        }

        // Check email format if provided
        if (record.googleEmail && record.googleEmail.trim() !== '') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(record.googleEmail)) {
            isValid = false
            validationErrors.push({
              row: rowNumber,
              message: 'Format email tidak valid',
              data: record,
            })
          }
        }

        // Check username length
        if (record.username) {
          if (record.username.length < 3) {
            isValid = false
            validationErrors.push({
              row: rowNumber,
              message: 'Username minimal 3 karakter',
              data: record,
            })
          } else if (record.username.length > 50) {
            isValid = false
            validationErrors.push({
              row: rowNumber,
              message: 'Username maksimal 50 karakter',
              data: record,
            })
          }
        }

        // Check password length
        if (record.password && record.password.length < 6) {
          isValid = false
          validationErrors.push({
            row: rowNumber,
            message: 'Password minimal 6 karakter',
            data: record,
          })
        }

        if (isValid) {
          validRecords++
        }
      })

      setCsvValidationErrors(validationErrors)
      setTotalValidRecords(validRecords)

      if (validationErrors.length > 0) {
        setShowCsvValidationErrors(true)
      } else {
        toast({
          title: 'Validasi Berhasil',
          description: `${records.length} data valid dan siap untuk diupload`,
        })
      }

      // Move to validation step regardless of errors
      // so user can see what needs to be fixed
      setUploadStep('validate')
    } catch (error) {
      console.error('Error validating CSV:', error)
      toast({
        title: 'Error',
        description: 'Gagal memvalidasi file CSV',
        variant: 'destructive',
      })
    } finally {
      setIsValidating(false)
    }
  }

  const handleBulkUploadSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!csvFile) {
      toast({
        title: 'Error',
        description: 'Harap pilih file CSV',
        variant: 'destructive',
      })
      return
    }

    if (uploadStep === 'select') {
      // First validate the file
      await validateCsvFile()
      return
    }

    if (uploadStep === 'validate' && csvValidationErrors.length > 0) {
      const continueAnyway = window.confirm(
        `Terdapat ${csvValidationErrors.length} error dalam file CSV. Hanya ${totalValidRecords} data yang valid. Lanjutkan upload data yang valid saja?`
      )

      if (!continueAnyway) {
        return
      }
    }

    // Proceed with upload
    setUploadStep('upload')

    try {
      setIsUploading(true)
      setUploadProgress(null)
      setUploadErrors([])

      const formData = new FormData()
      formData.append('file', csvFile)

      const response = await fetch('/api/users/bulk-upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        throw new Error(`Failed to upload: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      setUploadProgress({
        success: result.results.success,
        failed: result.results.failed,
        total: result.results.total,
      })

      if (result.results.errors && result.results.errors.length > 0) {
        setUploadErrors(result.results.errors)
      }

      toast({
        title: 'Bulk Upload Berhasil',
        description: result.message,
      })

      if (result.results.success > 0) {
        await fetchUsers() // Refresh user list
      }
    } catch (error) {
      console.error('Error uploading CSV:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal upload CSV',
        variant: 'destructive',
      })
    } finally {
      setIsUploading(false)
    }
  }

  const handleResetUpload = () => {
    setCsvFile(null)
    setCsvPreviewData([])
    setCsvValidationErrors([])
    setUploadStep('select')
    setUploadProgress(null)
    setUploadErrors([])
  }

  const handleEditUser = (user: User) => {
    setDialogMode('edit')
    setSelectedUser(user)

    // Pastikan classId diatur dengan benar untuk student
    let classIdValue = ''

    if (user.role === 'student') {
      // Jika user adalah student, gunakan classId yang tersedia atau 'none'
      classIdValue = user.classId ? String(user.classId) : 'none'
    }

    setFormData({
      id: user.uniqueCode || user.username || '',
      name: user.name,
      role: user.role,
      password: '',
      classId: classIdValue,
    })

    console.log('Edit user data:', {
      id: user.id,
      name: user.name,
      role: user.role,
      classId: user.classId,
      className: user.className,
    })

    setShowDialog(true)
  }

  const handleDeleteClick = async (user: User) => {
    setSelectedUser(user)
    setDeleteError(null)

    // Set loading state untuk pemeriksaan absensi
    setIsCheckingAttendance(false) // Reset state

    // Jika role student, periksa data absensi terlebih dahulu
    if (user.role === 'student') {
      try {
        setIsCheckingAttendance(true)
        const response = await fetch(`/api/users/${user.id}/check-attendance`)

        if (response.ok) {
          const data = await response.json()

          // Simpan info keberadaan data absensi ke state
          setSelectedUser({
            ...user,
            hasAttendanceRecords: data.hasAttendanceRecords,
          })
        } else {
          console.error('Error checking attendance records:', await response.text())
        }
      } catch (error) {
        console.error('Error checking attendance records:', error)
      } finally {
        setIsCheckingAttendance(false)
      }
    }
    // Untuk admin, langsung tampilkan dialog tanpa pemeriksaan data absensi

    setShowDeleteDialog(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedUser) return

    try {
      setIsSubmitting(true)
      setDeleteError(null)

      const response = await fetch(`/api/users/${selectedUser.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || errorData.message || 'Failed to delete user')
      }

      // Close the dialog and refresh the data
      setShowDeleteDialog(false)
      fetchUsers()

      toast({
        title: 'User dihapus',
        description: `${selectedUser.name} telah dihapus dari sistem`,
      })
    } catch (error) {
      console.error('Error deleting user:', error)

      // Display more user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Gagal menghapus pengguna'

      // Set the error message in state
      setDeleteError(errorMessage)

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Clear any previous errors
    setFormErrors({})

    // Password validation
    // Untuk mode add, password selalu wajib
    if ((dialogMode as string) === 'add' && formData.password.length < 6) {
      setFormErrors(prev => ({
        ...prev,
        password: 'Password minimal 6 karakter',
      }))
      setShowPasswordAlert(true)
      return
    }

    // Untuk mode edit, password opsional tapi jika diisi harus minimal 6 karakter
    if (
      (dialogMode as string) === 'edit' &&
      formData.password &&
      formData.password.length > 0 &&
      formData.password.length < 6
    ) {
      setFormErrors(prev => ({
        ...prev,
        password: 'Password minimal 6 karakter',
      }))
      setShowPasswordAlert(true)
      return
    }

    setIsSubmitting(true)

    try {
      let endpoint = '/api/users'
      let method = 'POST'
      let requestData: Record<string, any> = {}

      // Prepare request data based on role
      if ((dialogMode as string) === 'add') {
        // Add user
        if (formData.role === 'student') {
          // Student
          const classId =
            formData.classId === 'none'
              ? null
              : formData.classId && formData.classId.trim()
                ? parseInt(formData.classId, 10)
                : null

          requestData = {
            role: formData.role,
            name: formData.name,
            username: formData.id,
            password: formData.password,
            classId: classId,
          }
        } else {
          // Admin or Super Admin
          requestData = {
            role: formData.role,
            name: formData.name,
            username: formData.id,
            password: formData.password,
          }
        }
      } else {
        // Edit user
        if (!selectedUser) throw new Error('No user selected for edit')

        endpoint = `/api/users/${selectedUser.id}`
        method = 'PATCH'

        if (formData.role === 'student') {
          // Student
          const classId =
            formData.classId === 'none'
              ? null
              : formData.classId && formData.classId.trim()
                ? parseInt(formData.classId, 10)
                : null

          requestData = {
            role: formData.role,
            name: formData.name,
            classId: classId,
          }

          // Log untuk debugging
          console.log('Submitting student update with data:', {
            ...requestData,
            originalClassId: formData.classId,
            selectedUserClassId: selectedUser?.classId,
            parsedClassId: classId,
          })
        } else {
          // Admin or Super Admin
          requestData = {
            role: formData.role,
            name: formData.name,
            ...(formData.password ? { password: formData.password } : {}),
          }
        }
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      const responseText = await response.text()

      if (!response.ok) {
        let errorMessage = 'Unknown error'
        try {
          // Try to parse the response as JSON if it's not empty
          if (responseText && responseText.trim()) {
            try {
              const errorData = JSON.parse(responseText)
              console.error('API Error Response:', errorData)

              errorMessage =
                errorData.error ||
                errorData.message ||
                `Failed to ${(dialogMode as string) === 'add' ? 'add' : 'update'} user`
            } catch (e) {
              console.error('Failed to parse JSON response:', e)
              errorMessage = responseText
            }
          } else {
            // Handle empty response
            console.error('API Error: Empty response')
            errorMessage = `Failed to ${(dialogMode as string) === 'add' ? 'add' : 'update'} user (Empty response)`
          }
        } catch (e) {
          // If parsing fails, use the raw response text
          console.error('Failed to parse error response as JSON:', e)
          errorMessage =
            responseText || `Failed to ${(dialogMode as string) === 'add' ? 'add' : 'update'} user`
        }
        throw new Error(errorMessage)
      }

      let data
      try {
        // Try to parse the response as JSON
        data = JSON.parse(responseText)
      } catch (e) {}

      // Close the dialog and refresh the data
      setShowDialog(false)
      fetchUsers()

      toast({
        title: (dialogMode as string) === 'add' ? 'User ditambahkan' : 'User diperbarui',
        description:
          (dialogMode as string) === 'add'
            ? `${formData.name} telah ditambahkan sebagai ${formData.role}`
            : `Data ${formData.name} telah diperbarui`,
      })
    } catch (error) {
      console.error(
        `Error ${(dialogMode as string) === 'add' ? 'adding' : 'updating'} user:`,
        error
      )
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : `Gagal ${(dialogMode as string) === 'add' ? 'menambahkan' : 'memperbarui'} pengguna`,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Password change handler with validation
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setFormData({ ...formData, password })

    // Clear error when user starts typing
    if (formErrors.password) {
      setFormErrors({})
    }
  }

  const handleBulkDelete = () => {
    if (selectedUsers.size === 0) {
      toast({
        title: 'Error',
        description: 'Harap pilih minimal satu user untuk dihapus',
        variant: 'destructive',
      })
      return
    }

    setShowBulkDeleteDialog(true)
    setDeleteProgress(null)
    setDeleteErrors([])
    setShowDeleteErrors(false)
  }

  const handleBulkDeleteSubmit = async () => {
    if (selectedUsers.size === 0) return

    try {
      setIsDeleting(true)
      setDeleteProgress(null)
      setDeleteErrors([])

      const response = await fetch('/api/users/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userIds: Array.from(selectedUsers),
          deleteAttendanceRecords,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to delete: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      setDeleteProgress({
        success: result.results.success,
        failed: result.results.failed,
        total: result.results.total,
      })

      if (result.results.errors && result.results.errors.length > 0) {
        setDeleteErrors(result.results.errors)
      }

      toast({
        title: 'Bulk Delete Berhasil',
        description: result.message,
      })

      if (result.results.success > 0) {
        await fetchUsers() // Refresh user list
        setSelectedUsers(new Set()) // Clear selections
      }
    } catch (error) {
      console.error('Error deleting users:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal menghapus users',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const toggleUserSelection = (userId: number) => {
    const newSelected = new Set(selectedUsers)
    if (newSelected.has(userId)) {
      newSelected.delete(userId)
    } else {
      newSelected.add(userId)
    }
    setSelectedUsers(newSelected)
  }

  const toggleAllUsers = () => {
    if (selectedUsers.size === paginatedUsers.length) {
      // Deselect all visible users
      setSelectedUsers(new Set())
    } else {
      // Select all visible users
      const newSelected = new Set(selectedUsers)
      paginatedUsers.forEach(user => newSelected.add(user.id))
      setSelectedUsers(newSelected)
    }
  }

  // Add function to clear all selections
  const clearAllSelections = () => {
    setSelectedUsers(new Set())
  }

  const handleSort = (key: string) => {
    // If clicking on the same column, toggle direction
    if (sortConfig && sortConfig.key === key) {
      setSortConfig({
        key,
        direction: sortConfig.direction === 'ascending' ? 'descending' : 'ascending',
      })
    } else {
      // Default to ascending for first click
      setSortConfig({ key, direction: 'ascending' })
    }
  }

  const renderSortIndicator = (key: string) => {
    if (sortConfig?.key !== key) {
      return <span className="ml-1 text-gray-400">↕</span>
    }
    return <span className="ml-1">{sortConfig.direction === 'ascending' ? '↑' : '↓'}</span>
  }

  const handleBulkClassUpdate = () => {
    // Count how many students are selected
    const selectedStudents = Array.from(selectedUsers).filter(id => {
      const user = users.find(u => u.id === id)
      return user && user.role === 'student'
    })

    if (selectedStudents.length === 0) {
      toast({
        title: 'Error',
        description: 'Harap pilih minimal satu siswa untuk diperbarui kelasnya',
        variant: 'destructive',
      })
      return
    }

    if (classes.length === 0) {
      toast({
        title: 'Error',
        description: 'Tidak ada kelas tersedia untuk dipilih',
        variant: 'destructive',
      })
      return
    }

    setShowBulkUpdateClassDialog(true)
    setUpdateClassProgress(null)
    setUpdateClassErrors([])
    setShowUpdateClassErrors(false)
    setTargetClassId('')
  }

  const handleBulkClassUpdateSubmit = async () => {
    if (!targetClassId) {
      toast({
        title: 'Error',
        description: 'Harap pilih kelas target',
        variant: 'destructive',
      })
      return
    }

    try {
      setIsUpdatingClass(true)
      setUpdateClassProgress(null)
      setUpdateClassErrors([])

      // Get only selected student IDs (not admins)
      const selectedStudentIds = Array.from(selectedUsers).filter(id => {
        const user = users.find(u => u.id === id)
        return user && user.role === 'student'
      })

      if (selectedStudentIds.length === 0) {
        throw new Error('Tidak ada siswa yang dipilih')
      }

      const response = await fetch('/api/users/bulk-update-class', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetClassId: parseInt(targetClassId, 10),
          studentIds: selectedStudentIds,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()

      setUpdateClassProgress({
        success: result.results.success,
        failed: result.results.failed,
        total: result.results.total,
      })

      if (result.results.errors && result.results.errors.length > 0) {
        setUpdateClassErrors(result.results.errors)
      }

      toast({
        title: 'Bulk Update Kelas Berhasil',
        description: result.message,
      })

      if (result.results.success > 0) {
        await fetchUsers() // Refresh user list
      }
    } catch (error) {
      console.error('Error updating class:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal memperbarui kelas',
        variant: 'destructive',
      })
    } finally {
      setIsUpdatingClass(false)
    }
  }

  // If permission is loading, show a loading state
  if (permissionLoading) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen User</h1>
          <ThemeToggle />
        </header>

        <main className="container mx-auto px-4">
          <Card className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
            <span className="ml-2 text-slate-500">Memeriksa izin akses...</span>
          </Card>
        </main>

        <AdminBottomNav activeTab="users" adminRole={admin?.role} />
      </div>
    )
  }

  // If the user is not a super_admin, show an access denied message
  // The hook will automatically redirect, but we show this message in case there's a delay
  if (permission && !permission.isSuperAdmin) {
    return (
      <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
        <header className="flex items-center justify-between p-4">
          <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Siswa</h1>
          <ThemeToggle />
        </header>

        <main className="container mx-auto px-4">
          <Card className="p-8 text-center">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h2 className="mb-2 text-xl font-bold text-red-500">Akses Ditolak</h2>
            <p className="mb-4 text-slate-600 dark:text-slate-400">
              Anda tidak memiliki izin untuk mengakses halaman ini. Hanya Super Admin yang dapat
              mengakses halaman Manajemen Siswa.
            </p>
            <Button
              onClick={() => router.push('/admin/home')}
              className="bg-indigo-600 text-white hover:bg-indigo-700"
            >
              Kembali ke Beranda
            </Button>
          </Card>
        </main>

        <AdminBottomNav activeTab="users" adminRole={admin?.role} />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 pb-16 dark:bg-slate-900">
      <header className="flex items-center justify-between p-4">
        <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Manajemen Siswa</h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto px-4">
        {apiStatus && (
          <div className="mb-4 rounded-md bg-yellow-50 p-4 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200">
            <p className="text-sm font-medium">Peringatan: {apiStatus}</p>
            <p className="mt-1 text-xs">
              Endpoint API untuk manajemen user (/api/users) mungkin belum diimplementasikan. Fitur
              ini tidak akan berfungsi sampai API tersedia.
            </p>
          </div>
        )}

        <Card>
          <CardHeader className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div className="space-y-2">
              <CardTitle>Daftar Pengguna</CardTitle>
            </div>
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
              <Button onClick={handleAddUser} className="flex gap-1 sm:flex-nowrap">
                <Plus className="h-4 w-4" />
                <span className="sm:hidden">Tambah</span>
                <span className="hidden sm:inline">Tambah User</span>
              </Button>
              <Button
                onClick={handleBulkUpload}
                variant="outline"
                className="flex gap-1 sm:flex-nowrap"
              >
                <Upload className="h-4 w-4" />
                <span className="sm:hidden">Bulk</span>
                <span className="hidden sm:inline">Bulk Upload</span>
              </Button>
              <QrDownloadButton onClick={qrDownload.openDialog} disabled={isLoading} />
              {selectedUsers.size > 0 && (
                <>
                  <Button
                    onClick={handleBulkClassUpdate}
                    variant="outline"
                    className="flex gap-1 sm:flex-nowrap"
                  >
                    <SwitchCamera className="h-4 w-4" />
                    <span className="sm:hidden">Kelas</span>
                    <span className="hidden sm:inline">Update Kelas</span>
                    <span className="ml-1">
                      (
                      {
                        Array.from(selectedUsers).filter(id => {
                          const user = users.find(u => u.id === id)
                          return user && user.role === 'student'
                        }).length
                      }
                      )
                    </span>
                  </Button>
                  <Button
                    onClick={handleBulkDelete}
                    variant="destructive"
                    className="flex gap-1 sm:flex-nowrap"
                  >
                    <Trash className="h-4 w-4" />
                    <span>Hapus ({selectedUsers.size})</span>
                  </Button>
                  <Button
                    onClick={clearAllSelections}
                    variant="outline"
                    className="flex gap-1 sm:flex-nowrap"
                    title="Batalkan semua seleksi"
                  >
                    <XCircle className="h-4 w-4" />
                    <span className="sm:hidden">Clear</span>
                    <span className="hidden sm:inline">Clear Seleksi</span>
                  </Button>
                </>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
              {/* Search and filter */}
              <div className="flex flex-1 flex-col space-y-4 md:flex-row md:space-x-4 md:space-y-0">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-slate-400" />
                  <Input
                    placeholder="Cari nama, username, kode, atau kelas..."
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>

                {/* Role filter removed since this page is for students only */}

                <Select value={classFilter} onValueChange={setClassFilter}>
                  <SelectTrigger className="w-full md:w-[180px]">
                    <SelectValue placeholder="Filter kelas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Kelas</SelectItem>
                    {classes.map(cls => (
                      <SelectItem key={cls.id} value={String(cls.id)}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Student stats */}
        <div className="mb-4 mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
          <Card className="bg-white shadow-sm dark:bg-slate-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-500 dark:text-slate-400">
                Total Siswa
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {users.length}
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                {filteredUsers.length !== users.length &&
                  `${filteredUsers.length} ditampilkan dari filter`}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm dark:bg-slate-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-500 dark:text-slate-400">
                Siswa Berkelas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {users.filter(user => user.classId).length}
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                Siswa yang sudah memiliki kelas
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm dark:bg-slate-800">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-slate-500 dark:text-slate-400">
                Tanpa Kelas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                {users.filter(user => !user.classId).length}
              </div>
              <p className="text-xs text-slate-500 dark:text-slate-400">
                Siswa yang belum memiliki kelas
              </p>
            </CardContent>
          </Card>
        </div>

        <Card className="overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-indigo-600" />
              <span className="ml-2 text-slate-500">Memuat data...</span>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                          checked={
                            paginatedUsers.length > 0 &&
                            selectedUsers.size === paginatedUsers.length
                          }
                          onChange={toggleAllUsers}
                          title={
                            selectedUsers.size === paginatedUsers.length
                              ? 'Batalkan semua seleksi'
                              : 'Pilih semua pengguna di halaman ini'
                          }
                        />
                      </div>
                    </TableHead>
                    <TableHead className="w-[50px]">No</TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('identifier')}
                    >
                      ID/Kode Unik {renderSortIndicator('identifier')}
                    </TableHead>
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('name')}
                    >
                      Nama {renderSortIndicator('name')}
                    </TableHead>
                    {/* Role column removed since all users are students */}
                    <TableHead
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('className')}
                    >
                      Kelas {renderSortIndicator('className')}
                    </TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="h-24 text-center">
                        {users.length === 0
                          ? 'Tidak ada data pengguna'
                          : 'Tidak ada hasil yang cocok dengan pencarian'}
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedUsers.map((user, index) => (
                      <TableRow
                        key={user.id}
                        className={`hover:bg-indigo-50 dark:hover:bg-slate-800 ${
                          selectedUsers.has(user.id) ? 'bg-indigo-50 dark:bg-slate-800/60' : ''
                        }`}
                      >
                        <TableCell>
                          <input
                            type="checkbox"
                            className="h-5 w-5 cursor-pointer rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                            checked={selectedUsers.has(user.id)}
                            onChange={() => toggleUserSelection(user.id)}
                            title="Pilih/batalkan pilih pengguna ini"
                          />
                        </TableCell>
                        <TableCell className="text-center font-medium">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </TableCell>
                        <TableCell>
                          {user.uniqueCode
                            ? `${user.uniqueCode.substring(0, 8)}...`
                            : user.username || '-'}
                        </TableCell>
                        <TableCell>{user.name}</TableCell>
                        {/* Role cell removed since all users are students */}
                        <TableCell>{user.className || '-'}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditUser(user)}
                              disabled={isSubmitting || isLoading}
                            >
                              <Edit className="h-4 w-4" />
                              <span className="sr-only">Edit</span>
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-950"
                              onClick={() => handleDeleteClick(user)}
                              disabled={isSubmitting || isCheckingAttendance || isLoading}
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {filteredUsers.length > 0 && (
                <div className="flex flex-col items-center justify-between space-y-4 border-t border-slate-200 px-4 py-4 dark:border-slate-700 sm:flex-row sm:space-y-0 sm:px-6">
                  <div className="flex items-center space-x-4">
                    <p className="text-sm text-slate-700 dark:text-slate-400">
                      Menampilkan{' '}
                      <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>{' '}
                      sampai{' '}
                      <span className="font-medium">
                        {Math.min(currentPage * itemsPerPage, filteredUsers.length)}
                      </span>{' '}
                      dari <span className="font-medium">{filteredUsers.length}</span> pengguna
                    </p>

                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-slate-700 dark:text-slate-400">Tampilkan</span>
                      <Select
                        value={String(itemsPerPage)}
                        onValueChange={value => {
                          setItemsPerPage(Number(value))
                          setCurrentPage(1) // Reset to first page when changing items per page
                        }}
                      >
                        <SelectTrigger className="h-8 w-[70px]">
                          <SelectValue placeholder="10" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">5</SelectItem>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="20">20</SelectItem>
                          <SelectItem value="50">50</SelectItem>
                          <SelectItem value="100">100</SelectItem>
                        </SelectContent>
                      </Select>
                      <span className="text-sm text-slate-700 dark:text-slate-400">
                        per halaman
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-1 justify-between sm:justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="text-xs"
                    >
                      Sebelumnya
                    </Button>

                    <div className="mx-2 hidden items-center space-x-1 sm:flex">
                      {Array.from({ length: Math.ceil(filteredUsers.length / itemsPerPage) }).map(
                        (_, i) => (
                          <Button
                            key={i}
                            variant={currentPage === i + 1 ? 'default' : 'outline'}
                            size="sm"
                            className={`h-8 w-8 p-0 text-xs ${
                              Math.ceil(filteredUsers.length / itemsPerPage) > 7 &&
                              i !== 0 &&
                              i !== Math.ceil(filteredUsers.length / itemsPerPage) - 1 &&
                              Math.abs(currentPage - (i + 1)) > 1
                                ? 'hidden'
                                : ''
                            }`}
                            onClick={() => setCurrentPage(i + 1)}
                          >
                            {i + 1}
                          </Button>
                        )
                      )}
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage(prev =>
                          Math.min(prev + 1, Math.ceil(filteredUsers.length / itemsPerPage))
                        )
                      }
                      disabled={currentPage >= Math.ceil(filteredUsers.length / itemsPerPage)}
                      className="text-xs"
                    >
                      Selanjutnya
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </Card>
      </main>

      {/* Add/Edit Dialog */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              {(dialogMode as string) === 'add' ? (
                <>
                  <Plus className="mr-2 h-5 w-5 text-indigo-600" />
                  Tambah Siswa Baru
                </>
              ) : (
                <>
                  <Edit className="mr-2 h-5 w-5 text-indigo-600" />
                  Edit Siswa
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              {(dialogMode as string) === 'add'
                ? 'Masukkan informasi untuk siswa baru'
                : 'Perbarui informasi siswa'}
            </DialogDescription>
          </DialogHeader>

          {/* Role indicator untuk mode edit */}
          {(dialogMode as string) === 'edit' && selectedUser && (
            <div className="mb-4 flex items-center rounded-md bg-slate-50 p-3 dark:bg-slate-800/50">
              <div
                className={`mr-3 rounded-full p-2 ${
                  selectedUser.role === 'super_admin'
                    ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                    : selectedUser.role === 'admin'
                      ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'
                      : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                }`}
              >
                {selectedUser.role === 'super_admin' ? (
                  <Shield className="h-5 w-5" />
                ) : selectedUser.role === 'admin' ? (
                  <ShieldCheck className="h-5 w-5" />
                ) : (
                  <User2 className="h-5 w-5" />
                )}
              </div>
              <div>
                <p className="font-medium text-slate-800 dark:text-slate-200">
                  {selectedUser.name}
                </p>
                <p className="text-sm text-slate-600 dark:text-slate-400">
                  {selectedUser.role === 'student'
                    ? `Siswa ${selectedUser.className ? `- ${selectedUser.className}` : ''}`
                    : selectedUser.role === 'super_admin'
                      ? 'Super Admin'
                      : 'Admin'}
                </p>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Role is fixed to student for this page */}
            {(dialogMode as string) === 'add' && (
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <div className="flex items-center rounded-md bg-blue-50 p-3 dark:bg-blue-900/30">
                  <User2 className="mr-2 h-4 w-4 text-blue-600 dark:text-blue-400" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                    Siswa (Student)
                  </span>
                </div>
                <p className="text-xs text-slate-500">
                  Halaman ini khusus untuk mengelola data siswa
                </p>
              </div>
            )}

            {/* Auto-generated unique code info for students */}
            {(dialogMode as string) === 'add' && (
              <div className="mb-2 text-xs text-slate-500">
                Kode unik untuk siswa akan dibuat otomatis
              </div>
            )}

            {/* Username & Password untuk siswa */}
            {(dialogMode as string) === 'add' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    value={formData.id}
                    onChange={e => setFormData({ ...formData, id: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  {formErrors.password && (
                    <div className="mb-2 flex items-center text-sm text-red-500">
                      <AlertCircle className="mr-1 h-4 w-4" />
                      {formErrors.password}
                    </div>
                  )}
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={handlePasswordChange}
                    required
                    className={formErrors.password ? 'border-red-500' : ''}
                  />
                  <p className="text-xs text-slate-500">Password harus minimal 6 karakter</p>
                </div>
              </>
            )}

            {/* Name field */}
            <div className="space-y-2">
              <Label htmlFor="name">Nama</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                disabled={isSubmitting}
                required
              />
            </div>

            {/* Class selection for students */}
            <div className="space-y-2">
              <Label htmlFor="classId">Kelas</Label>
              {isLoadingClasses ? (
                <div className="flex items-center space-x-2 py-2">
                  <Loader2 className="h-4 w-4 animate-spin text-indigo-600" />
                  <span className="text-sm text-slate-500">Memuat data kelas...</span>
                </div>
              ) : classes.length > 0 ? (
                <Select
                  value={formData.classId}
                  onValueChange={value => setFormData({ ...formData, classId: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih kelas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Tidak Ada Kelas</SelectItem>
                    {classes.map(cls => (
                      <SelectItem key={cls.id} value={String(cls.id)}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <div className="mb-2 mt-1 text-sm text-amber-600 dark:text-amber-400">
                  <p>Tidak ada kelas tersedia. Silakan tambahkan kelas terlebih dahulu.</p>
                  <Button
                    type="button"
                    variant="link"
                    className="h-auto p-0 text-indigo-600 dark:text-indigo-400"
                    onClick={() => {
                      setShowDialog(false)
                      // Use Next.js router to navigate or a simple redirect
                      window.location.href = '/admin/classes'
                    }}
                  >
                    Pergi ke Manajemen Kelas
                  </Button>
                </div>
              )}
              <p className="text-xs text-slate-500">
                Kelas akan digunakan untuk mengelompokkan siswa
              </p>
            </div>

            {/* Admin password fields removed since this page is for students only */}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowDialog(false)}
                disabled={isSubmitting}
              >
                Batal
              </Button>
              <Button
                type="submit"
                className="bg-indigo-600 text-white hover:bg-indigo-700"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {(dialogMode as string) === 'add' ? 'Menambahkan...' : 'Menyimpan...'}
                  </>
                ) : (dialogMode as string) === 'add' ? (
                  'Tambah'
                ) : (
                  'Simpan'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <Trash2 className="mr-2 h-5 w-5" />
              Hapus User
            </DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus user ini? Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>

          {deleteError && (
            <Alert variant="destructive" className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}

          {isCheckingAttendance && (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="mr-2 h-5 w-5 animate-spin text-indigo-600" />
              <span className="text-sm text-slate-600">Memeriksa data absensi...</span>
            </div>
          )}

          {selectedUser && !isCheckingAttendance && (
            <div className="py-4">
              <div className="mb-4 flex items-center rounded-md bg-slate-50 p-3 dark:bg-slate-800/50">
                <div
                  className={`mr-3 rounded-full p-2 ${
                    selectedUser.role === 'super_admin'
                      ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
                      : selectedUser.role === 'admin'
                        ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  }`}
                >
                  {selectedUser.role === 'super_admin' ? (
                    <Shield className="h-5 w-5" />
                  ) : selectedUser.role === 'admin' ? (
                    <ShieldCheck className="h-5 w-5" />
                  ) : (
                    <User2 className="h-5 w-5" />
                  )}
                </div>
                <div>
                  <p className="font-medium text-slate-800 dark:text-slate-200">
                    {selectedUser.name}
                  </p>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {selectedUser.role === 'student'
                      ? `Siswa ${selectedUser.className ? `- ${selectedUser.className}` : ''}`
                      : selectedUser.role === 'super_admin'
                        ? 'Super Admin'
                        : 'Admin'}
                  </p>
                </div>
              </div>

              {/* Peringatan khusus untuk super_admin */}
              {selectedUser.role === 'super_admin' && (
                <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-800 dark:bg-red-900/30 dark:text-red-200">
                  <div className="flex items-start">
                    <AlertCircle className="mr-2 h-4 w-4 flex-shrink-0 translate-y-0.5" />
                    <div>
                      <p className="font-medium">Peringatan!</p>
                      <p className="mt-1 text-xs">
                        Anda akan menghapus pengguna dengan hak akses tertinggi (Super Admin).
                        Pastikan masih ada Super Admin lain dalam sistem, atau Anda mungkin tidak
                        dapat mengakses fitur manajemen pengguna.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Tampilkan warning hanya jika siswa memiliki data absensi */}
              {selectedUser.role === 'student' && selectedUser.hasAttendanceRecords && (
                <div className="mb-4 rounded-md bg-yellow-50 p-3 text-sm text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200">
                  <div className="flex items-start">
                    <AlertCircle className="mr-2 h-4 w-4 flex-shrink-0 translate-y-0.5" />
                    <div>
                      <p className="font-medium">Perhatian!</p>
                      <p className="mt-1 text-xs">
                        Menghapus siswa ini akan juga menghapus SEMUA data absensi yang terkait
                        dengan siswa tersebut. Tindakan ini tidak dapat dibatalkan dan akan
                        menghilangkan seluruh riwayat kehadiran.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Informasi jika siswa tidak memiliki data absensi */}
              {selectedUser.role === 'student' && selectedUser.hasAttendanceRecords === false && (
                <div className="mb-4 rounded-md bg-slate-50 p-3 text-sm text-slate-700 dark:bg-slate-800/30 dark:text-slate-300">
                  <div className="flex items-start">
                    <div>
                      <p className="mt-1 text-xs">
                        Siswa ini tidak memiliki data absensi yang tersimpan.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Peringatan untuk admin */}
              {selectedUser.role === 'admin' && (
                <div className="mb-4 rounded-md bg-amber-50 p-3 text-sm text-amber-800 dark:bg-amber-900/30 dark:text-amber-200">
                  <div className="flex items-start">
                    <AlertCircle className="mr-2 h-4 w-4 flex-shrink-0 translate-y-0.5" />
                    <div>
                      <p className="font-medium">Konfirmasi</p>
                      <p className="mt-1 text-xs">
                        Anda akan menghapus akun admin. Admin ini tidak akan lagi dapat mengakses
                        sistem.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter className="flex space-x-2 sm:justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              className="flex-1 sm:flex-none"
              disabled={isSubmitting}
            >
              Batal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isSubmitting || isCheckingAttendance}
              className="flex-1 sm:flex-none"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Menghapus...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Hapus
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Password Error Alert Dialog */}
      <AlertDialog open={showPasswordAlert} onOpenChange={setShowPasswordAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Password Terlalu Pendek</AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>Password harus minimal 6 karakter untuk keamanan yang lebih baik.</p>
              <div className="mt-2 rounded-md bg-amber-50 p-3 dark:bg-amber-900/20">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle
                      className="h-5 w-5 text-amber-600 dark:text-amber-400"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                      Rekomendasi keamanan
                    </h3>
                    <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                      <ul className="list-disc space-y-1 pl-5">
                        <li>Gunakan minimal 8 karakter untuk keamanan optimal</li>
                        <li>Kombinasikan huruf besar, huruf kecil, angka, dan simbol</li>
                        <li>Hindari menggunakan informasi pribadi atau kata-kata umum</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction
              className="bg-indigo-600 text-white hover:bg-indigo-700"
              onClick={() => setShowPasswordAlert(false)}
            >
              Mengerti
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Upload Dialog */}
      <Dialog
        open={showBulkUploadDialog}
        onOpenChange={open => {
          if (!open) {
            // Reset states when closing dialog
            handleResetUpload()
          }
          setShowBulkUploadDialog(open)
        }}
      >
        <DialogContent className="sm:max-w-[700px]">
          <DialogHeader>
            <DialogTitle>Bulk Upload Siswa</DialogTitle>
            <DialogDescription>
              Upload CSV dengan data siswa dalam jumlah besar. Format: name, username, password,
              className, nis, googleEmail, whatsapp.
            </DialogDescription>
          </DialogHeader>

          <div className="mb-3 flex items-center justify-between">
            <div className="flex space-x-4">
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  uploadStep === 'select' ? 'bg-indigo-600 text-white' : 'bg-gray-200'
                }`}
              >
                1
              </div>
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  uploadStep === 'validate' ? 'bg-indigo-600 text-white' : 'bg-gray-200'
                }`}
              >
                2
              </div>
              <div
                className={`flex h-8 w-8 items-center justify-center rounded-full ${
                  uploadStep === 'upload' ? 'bg-indigo-600 text-white' : 'bg-gray-200'
                }`}
              >
                3
              </div>
            </div>
            <div className="text-sm text-gray-500">
              {uploadStep === 'select' && 'Pilih File'}
              {uploadStep === 'validate' && 'Validasi'}
              {uploadStep === 'upload' && 'Upload'}
            </div>
          </div>

          <form onSubmit={handleBulkUploadSubmit}>
            {uploadStep === 'select' && (
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="csvFile">File CSV</Label>
                  <Input
                    id="csvFile"
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    disabled={isValidating}
                  />
                  <p className="text-xs text-muted-foreground">
                    CSV harus memiliki header: name, username, password, className, nis,
                    googleEmail, whatsapp
                  </p>
                </div>

                <div className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={downloadSampleCsv}
                    className="flex items-center gap-1"
                  >
                    <Download className="h-4 w-4" /> Download Sample CSV
                  </Button>
                </div>
              </div>
            )}

            {uploadStep === 'validate' && (
              <div className="grid gap-4 py-4">
                <div className="flex justify-between">
                  <h3 className="text-lg font-medium">Data Preview</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleResetUpload}
                    className="flex items-center gap-1"
                  >
                    <X className="h-4 w-4" /> Ganti File
                  </Button>
                </div>

                {csvPreviewData.length > 0 && (
                  <div className="max-h-64 overflow-auto rounded border">
                    <table className="w-full table-auto">
                      <thead className="bg-gray-50">
                        <tr>
                          {Object.keys(csvPreviewData[0]).map(header => (
                            <th
                              key={header}
                              className="border px-2 py-1 text-left text-xs font-medium uppercase text-gray-500"
                            >
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {csvPreviewData.map((row, rowIndex) => (
                          <tr
                            key={rowIndex}
                            className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}
                          >
                            {Object.values(row).map((cell: any, cellIndex) => (
                              <td key={cellIndex} className="border px-2 py-1 text-xs">
                                {cell || '-'}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}

                <div className="space-y-2">
                  <h4 className="font-medium">Status Validasi:</h4>
                  <div className="text-sm">
                    <p>Total: {totalValidRecords + csvValidationErrors.length} baris</p>
                    <p className="text-green-600 dark:text-green-400">
                      Valid: {totalValidRecords} siswa
                    </p>
                    <p className="text-red-600 dark:text-red-400">
                      Error: {csvValidationErrors.length} baris
                    </p>
                  </div>

                  {csvValidationErrors.length > 0 && (
                    <div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowCsvValidationErrors(!showCsvValidationErrors)}
                      >
                        {showCsvValidationErrors ? 'Sembunyikan Error' : 'Tampilkan Error'}
                      </Button>

                      {showCsvValidationErrors && (
                        <div className="mt-2 max-h-40 overflow-auto rounded border p-2 text-xs">
                          {csvValidationErrors.map((error, i) => (
                            <div key={i} className="mb-1 border-b pb-1">
                              <p>
                                <strong>Baris {error.row}:</strong> {error.message}
                              </p>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
                  <p>
                    {csvValidationErrors.length > 0
                      ? `Terdapat ${csvValidationErrors.length} error dalam file. Sebaiknya perbaiki file CSV dan upload ulang.`
                      : 'Semua data valid! Klik tombol Upload untuk mulai menambahkan siswa.'}
                  </p>
                </div>
              </div>
            )}

            {uploadStep === 'upload' && uploadProgress && (
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <h4 className="font-medium">Hasil Upload:</h4>
                  <div className="text-sm">
                    <p>Total: {uploadProgress.total} siswa</p>
                    <p className="text-green-600 dark:text-green-400">
                      Berhasil: {uploadProgress.success} siswa
                    </p>
                    <p className="text-red-600 dark:text-red-400">
                      Gagal: {uploadProgress.failed} siswa
                    </p>
                  </div>

                  {uploadErrors.length > 0 && (
                    <div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowUploadErrors(!showUploadErrors)}
                      >
                        {showUploadErrors ? 'Sembunyikan Error' : 'Tampilkan Error'}
                      </Button>

                      {showUploadErrors && (
                        <div className="mt-2 max-h-40 overflow-auto rounded border p-2 text-xs">
                          {uploadErrors.map((error, i) => (
                            <div key={i} className="mb-1 border-b pb-1">
                              <p>
                                <strong>Baris {error.row}:</strong> {error.message}
                              </p>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  if (uploadStep === 'validate') {
                    setUploadStep('select')
                  } else {
                    setShowBulkUploadDialog(false)
                    handleResetUpload()
                  }
                }}
                disabled={isUploading || isValidating}
              >
                {uploadStep === 'upload' ? 'Tutup' : 'Kembali'}
              </Button>

              {uploadStep !== 'upload' && (
                <Button
                  type={uploadStep === 'select' ? 'button' : 'submit'}
                  onClick={uploadStep === 'select' ? validateCsvFile : undefined}
                  disabled={!csvFile || isUploading || isValidating}
                >
                  {isValidating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span>Validating...</span>
                    </>
                  ) : isUploading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      <span>Uploading...</span>
                    </>
                  ) : uploadStep === 'select' ? (
                    'Validasi'
                  ) : (
                    'Upload'
                  )}
                </Button>
              )}
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Bulk Delete Dialog */}
      <Dialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-600">
              <Trash className="mr-2 h-5 w-5" />
              Hapus Banyak User
            </DialogTitle>
            <DialogDescription>
              Anda akan menghapus {selectedUsers.size} user. Tindakan ini tidak dapat dibatalkan.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="mb-4 rounded-md bg-red-50 p-3 text-sm text-red-800 dark:bg-red-900/30 dark:text-red-200">
              <div className="flex items-start">
                <AlertCircle className="mr-2 h-4 w-4 flex-shrink-0 translate-y-0.5" />
                <div>
                  <p className="font-medium">Peringatan!</p>
                  <p className="mt-1 text-xs">
                    Tindakan ini akan menghapus semua user yang dipilih dan tidak dapat dibatalkan.
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-4 space-y-2">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="deleteAttendanceRecords"
                  checked={deleteAttendanceRecords}
                  onChange={e => setDeleteAttendanceRecords(e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <Label htmlFor="deleteAttendanceRecords">Hapus data absensi siswa</Label>
              </div>
              <p className="text-xs text-slate-500">
                Jika dicentang, semua data absensi untuk siswa yang dihapus juga akan dihapus.
              </p>
            </div>

            {deleteProgress && (
              <div className="space-y-2">
                <h4 className="font-medium">Hasil Delete:</h4>
                <div className="text-sm">
                  <p>Total: {deleteProgress.total} user</p>
                  <p className="text-green-600 dark:text-green-400">
                    Berhasil: {deleteProgress.success} user
                  </p>
                  <p className="text-red-600 dark:text-red-400">
                    Gagal: {deleteProgress.failed} user
                  </p>
                </div>

                {deleteErrors.length > 0 && (
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowDeleteErrors(!showDeleteErrors)}
                    >
                      {showDeleteErrors ? 'Sembunyikan Error' : 'Tampilkan Error'}
                    </Button>

                    {showDeleteErrors && (
                      <div className="mt-2 max-h-40 overflow-auto rounded border p-2 text-xs">
                        {deleteErrors.map((error, i) => (
                          <div key={i} className="mb-1 border-b pb-1">
                            <p>
                              <strong>ID {error.userId}:</strong> {error.message}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowBulkDeleteDialog(false)}
              disabled={isDeleting}
            >
              Batal
            </Button>
            <Button
              variant="destructive"
              onClick={handleBulkDeleteSubmit}
              disabled={isDeleting || selectedUsers.size === 0}
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span>Menghapus...</span>
                </>
              ) : (
                <>
                  <Trash className="mr-2 h-4 w-4" />
                  <span>Hapus {selectedUsers.size} User</span>
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Bulk Update Class Dialog */}
      <Dialog open={showBulkUpdateClassDialog} onOpenChange={setShowBulkUpdateClassDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <SwitchCamera className="mr-2 h-5 w-5 text-indigo-600" />
              Update Kelas Siswa Terpilih
            </DialogTitle>
            <DialogDescription>Pindahkan siswa yang dipilih ke kelas baru.</DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {/* Count and display selected students information */}
            <div className="mb-4 rounded-md bg-blue-50 p-3 text-sm text-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
              <div className="flex items-start">
                <div>
                  <p className="font-medium">Siswa Terpilih:</p>
                  <p className="mt-1">
                    {
                      Array.from(selectedUsers).filter(id => {
                        const user = users.find(u => u.id === id)
                        return user && user.role === 'student'
                      }).length
                    }{' '}
                    siswa akan dipindahkan ke kelas baru.
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-4 space-y-2">
              <Label htmlFor="targetClassId">Kelas Target</Label>
              {isLoadingClasses ? (
                <div className="flex items-center space-x-2 py-2">
                  <Loader2 className="h-4 w-4 animate-spin text-indigo-600" />
                  <span className="text-sm text-slate-500">Memuat data kelas...</span>
                </div>
              ) : classes.length > 0 ? (
                <Select value={targetClassId} onValueChange={setTargetClassId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih kelas target" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map(cls => (
                      <SelectItem key={cls.id} value={String(cls.id)}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <div className="mb-2 text-sm text-amber-600 dark:text-amber-400">
                  <p>Tidak ada kelas tersedia.</p>
                </div>
              )}
              <p className="text-xs text-slate-500">
                Siswa yang dipilih akan dipindahkan ke kelas ini
              </p>
            </div>

            {updateClassProgress && (
              <div className="space-y-2">
                <h4 className="font-medium">Hasil Update Kelas:</h4>
                <div className="text-sm">
                  <p>Total: {updateClassProgress.total} siswa</p>
                  <p className="text-green-600 dark:text-green-400">
                    Berhasil: {updateClassProgress.success} siswa
                  </p>
                  <p className="text-red-600 dark:text-red-400">
                    Gagal: {updateClassProgress.failed} siswa
                  </p>
                </div>

                {updateClassErrors.length > 0 && (
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowUpdateClassErrors(!showUpdateClassErrors)}
                    >
                      {showUpdateClassErrors ? 'Sembunyikan Error' : 'Tampilkan Error'}
                    </Button>

                    {showUpdateClassErrors && (
                      <div className="mt-2 max-h-40 overflow-auto rounded border p-2 text-xs">
                        {updateClassErrors.map((error, i) => (
                          <div key={i} className="mb-1 border-b pb-1">
                            <p>
                              <strong>
                                {error.name} (ID {error.userId}):
                              </strong>{' '}
                              {error.message}
                            </p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowBulkUpdateClassDialog(false)}
              disabled={isUpdatingClass}
            >
              {updateClassProgress ? 'Tutup' : 'Batal'}
            </Button>
            {!updateClassProgress && (
              <Button
                variant="default"
                onClick={handleBulkClassUpdateSubmit}
                disabled={isUpdatingClass || !targetClassId}
              >
                {isUpdatingClass ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    <span>Memperbarui...</span>
                  </>
                ) : (
                  <>
                    <SwitchCamera className="mr-2 h-4 w-4" />
                    <span>Update Kelas</span>
                  </>
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* QR Download Dialog */}
      <QrDownloadDialog
        showDialog={qrDownload.showDialog}
        downloadType={qrDownload.downloadType}
        classId={qrDownload.classId}
        isDownloading={qrDownload.isDownloading}
        progress={qrDownload.progress}
        closeDialog={qrDownload.closeDialog}
        setDownloadType={qrDownload.setDownloadType}
        setClassId={qrDownload.setClassId}
        downloadQrCodes={qrDownload.downloadQrCodes}
        users={users}
        classes={classes}
        selectedUsers={selectedUsers}
        isLoadingClasses={isLoadingClasses}
      />

      {/* Bottom navigation - hanya tampil di mobile */}
      <AdminBottomNav activeTab="users" adminRole={admin?.role} />
    </div>
  )
}
