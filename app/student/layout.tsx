'use client'
import type React from 'react'
import { StudentLayout } from '@/components/layouts/student-layout'
import { usePathname } from 'next/navigation'
import { SessionProvider } from 'next-auth/react'

// Wrapper component to conditionally apply layout
export default function StudentRootLayout({ children }: { children: React.ReactNode }) {
  // This is a client component that will check the path
  return (
    <SessionProvider>
      <StudentLayoutWrapper>{children}</StudentLayoutWrapper>
    </SessionProvider>
  )
}

function StudentLayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()

  // Don't apply layout to login, forgot-password, or reset-password pages
  if (
    pathname === '/student' ||
    pathname === '/student/forgot-password' ||
    pathname.startsWith('/student/reset-password')
  ) {
    return <>{children}</>
  }

  // Apply layout to all other student pages
  return <StudentLayout>{children}</StudentLayout>
}
