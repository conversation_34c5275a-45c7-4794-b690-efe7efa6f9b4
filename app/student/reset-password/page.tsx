'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ThemeToggle } from '@/components/theme-toggle'
import { useToast } from '@/components/ui/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { AlertCircle, Check, X, LockKeyhole } from 'lucide-react'
import { Progress } from '@/components/ui/progress'

// Loading component for the Suspense boundary
function LoadingState() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-4 dark:from-slate-900 dark:to-slate-800">
      <div className="absolute right-4 top-4">
        <ThemeToggle />
      </div>
      <Card className="w-full max-w-md border-indigo-100 shadow-md dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Loading...</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-6">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-indigo-600"></div>
        </CardContent>
      </Card>
    </div>
  )
}

// Password strength check
const checkPasswordStrength = (
  password: string
): {
  score: number
  feedback: string
  color: string
} => {
  let score = 0
  const checks = []

  if (password.length >= 8) {
    score += 1
    checks.push(true)
  } else {
    checks.push(false)
  }

  if (/[A-Z]/.test(password)) {
    score += 1
    checks.push(true)
  } else {
    checks.push(false)
  }

  if (/[a-z]/.test(password)) {
    score += 1
    checks.push(true)
  } else {
    checks.push(false)
  }

  if (/[0-9]/.test(password)) {
    score += 1
    checks.push(true)
  } else {
    checks.push(false)
  }

  if (/[^A-Za-z0-9]/.test(password)) {
    score += 1
    checks.push(true)
  } else {
    checks.push(false)
  }

  // Normalize score to 0-100
  const normalizedScore = (score / 5) * 100

  let feedback = ''
  let color = ''

  if (normalizedScore === 0) {
    feedback = ''
    color = 'bg-slate-200 dark:bg-slate-700'
  } else if (normalizedScore <= 20) {
    feedback = 'Sangat Lemah'
    color = 'bg-red-500'
  } else if (normalizedScore <= 40) {
    feedback = 'Lemah'
    color = 'bg-orange-500'
  } else if (normalizedScore <= 60) {
    feedback = 'Sedang'
    color = 'bg-yellow-500'
  } else if (normalizedScore <= 80) {
    feedback = 'Kuat'
    color = 'bg-lime-500'
  } else {
    feedback = 'Sangat Kuat'
    color = 'bg-green-500'
  }

  return { score: normalizedScore, feedback, color }
}

// Main component that uses useSearchParams
function ResetPasswordForm() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState<{
    message: string
    type: 'rateLimit' | 'otpInvalid' | 'general'
  } | null>(null)
  const [passwordFocused, setPasswordFocused] = useState(false)

  const passwordStrength = checkPasswordStrength(newPassword)
  const passwordsMatch = newPassword === confirmPassword && confirmPassword !== ''

  // Parse query parameters
  const method = searchParams.get('method')
  const token = searchParams.get('token')
  const whatsapp = searchParams.get('whatsapp')
  const otp = searchParams.get('otp')

  // Validate query parameters
  useEffect(() => {
    // For WhatsApp method, we need both whatsapp and otp
    if (method === 'whatsapp' && (!whatsapp || !otp)) {
      toast({
        title: 'Error',
        description: 'Invalid reset password parameters. Please start over.',
        variant: 'destructive',
      })
      router.push('/student/forgot-password')
      return
    }

    // For token method, we need the token
    if (method === 'token' && !token) {
      toast({
        title: 'Error',
        description: 'Invalid reset password link',
        variant: 'destructive',
      })
      router.push('/student/forgot-password')
      return
    }

    // If no method is specified, we need a token
    if (!method && !token) {
      toast({
        title: 'Error',
        description: 'Invalid reset password link',
        variant: 'destructive',
      })
      router.push('/student/forgot-password')
    }
  }, [method, token, whatsapp, otp, router, toast])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    // Password confirmation validation
    if (newPassword !== confirmPassword) {
      toast({
        title: 'Error',
        description: 'Password tidak cocok',
        variant: 'destructive',
      })
      setError({
        message: 'Password tidak cocok. Silakan pastikan kedua password sama.',
        type: 'general',
      })
      return
    }

    // Password strength validation (minimum requirements)
    if (passwordStrength.score < 40) {
      toast({
        title: 'Password Terlalu Lemah',
        description: 'Password harus lebih kuat. Tambahkan huruf kapital, angka, atau simbol.',
        variant: 'destructive',
      })
      setError({
        message:
          'Password terlalu lemah. Minimal mengandung kombinasi huruf kecil, kapital, dan angka.',
        type: 'general',
      })
      return
    }

    setLoading(true)

    try {
      // Prepare request payload based on verification method
      let payload: any = { newPassword }

      if (token) {
        payload.token = token
      } else if (method === 'whatsapp') {
        payload.whatsapp = whatsapp
        payload.otp = otp
      } else {
        throw new Error('Invalid reset method')
      }

      const response = await fetch('/api/auth/password-reset/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'Password diperbarui',
          description: 'Password Anda telah berhasil diperbarui',
        })
        router.push('/student')
      } else {
        // Handle specific error types
        if (response.status === 429 || data.error?.toLowerCase().includes('too many')) {
          setError({
            message: data.error || 'Terlalu banyak percobaan gagal. Silakan mulai dari awal.',
            type: 'rateLimit',
          })
        } else if (
          data.error?.toLowerCase().includes('invalid otp') ||
          data.error?.toLowerCase().includes('otp expired')
        ) {
          setError({
            message:
              data.error || 'OTP tidak valid atau sudah kedaluwarsa. Silakan mulai dari awal.',
            type: 'otpInvalid',
          })

          // Redirect back to forgot-password to restart the process
          setTimeout(() => {
            router.push('/student/forgot-password')
          }, 3000)
        } else {
          setError({
            message: data.error || 'Terjadi kesalahan, silakan coba lagi',
            type: 'general',
          })
        }

        toast({
          title: 'Gagal',
          description: data.error || 'Terjadi kesalahan, silakan coba lagi',
          variant: 'destructive',
        })
      }
    } catch (error) {
      setError({
        message: 'Terjadi kesalahan, silakan coba lagi',
        type: 'general',
      })
      toast({
        title: 'Gagal',
        description: 'Terjadi kesalahan, silakan coba lagi',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-indigo-50 to-white p-4 dark:from-slate-900 dark:to-slate-800">
      <div className="absolute right-4 top-4">
        <ThemeToggle />
      </div>

      <Card className="w-full max-w-md border-indigo-100 shadow-md dark:border-slate-700">
        <CardHeader className="space-y-1 text-center">
          <CardTitle className="text-2xl font-bold">Reset Password</CardTitle>
          <CardDescription>Buat password baru untuk akun Anda</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert
              variant={error.type === 'rateLimit' ? 'destructive' : 'default'}
              className="mb-4"
            >
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                {error.type === 'rateLimit'
                  ? 'Batas Percobaan Terlampaui'
                  : error.type === 'otpInvalid'
                    ? 'OTP Tidak Valid'
                    : 'Error'}
              </AlertTitle>
              <AlertDescription>{error.message}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newPassword">Password Baru</Label>
              <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={e => setNewPassword(e.target.value)}
                onFocus={() => setPasswordFocused(true)}
                placeholder="Masukkan password baru"
                required
                minLength={6}
              />

              {(passwordFocused || newPassword.length > 0) && (
                <div className="mt-2 space-y-2">
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-slate-500">
                        Kekuatan Password: {passwordStrength.feedback}
                      </span>
                      <span
                        className="text-xs font-medium"
                        style={{ color: passwordStrength.color }}
                      >
                        {passwordStrength.score}%
                      </span>
                    </div>
                    <Progress
                      value={passwordStrength.score}
                      className="h-1.5"
                      indicatorClassName={passwordStrength.color}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-x-4 gap-y-1">
                    <div className="flex items-center gap-1.5">
                      {/[A-Z]/.test(newPassword) ? (
                        <Check className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <X className="h-3.5 w-3.5 text-slate-400" />
                      )}
                      <span className="text-xs">Huruf kapital</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      {/[a-z]/.test(newPassword) ? (
                        <Check className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <X className="h-3.5 w-3.5 text-slate-400" />
                      )}
                      <span className="text-xs">Huruf kecil</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      {/[0-9]/.test(newPassword) ? (
                        <Check className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <X className="h-3.5 w-3.5 text-slate-400" />
                      )}
                      <span className="text-xs">Angka</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      {/[^A-Za-z0-9]/.test(newPassword) ? (
                        <Check className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <X className="h-3.5 w-3.5 text-slate-400" />
                      )}
                      <span className="text-xs">Karakter khusus</span>
                    </div>
                    <div className="flex items-center gap-1.5">
                      {newPassword.length >= 8 ? (
                        <Check className="h-3.5 w-3.5 text-green-500" />
                      ) : (
                        <X className="h-3.5 w-3.5 text-slate-400" />
                      )}
                      <span className="text-xs">Min. 8 karakter</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                placeholder="Konfirmasi password baru"
                required
                minLength={6}
              />

              {confirmPassword && (
                <div className="mt-1 flex items-center gap-1.5">
                  {passwordsMatch ? (
                    <>
                      <Check className="h-3.5 w-3.5 text-green-500" />
                      <span className="text-xs text-green-500">Password cocok</span>
                    </>
                  ) : (
                    <>
                      <X className="h-3.5 w-3.5 text-red-500" />
                      <span className="text-xs text-red-500">Password tidak cocok</span>
                    </>
                  )}
                </div>
              )}
            </div>

            <Alert variant="info" className="py-2">
              <LockKeyhole className="h-4 w-4" />
              <AlertDescription className="text-xs">
                Password baru harus berbeda dengan password lama. Pastikan password Anda cukup kuat
                dan mudah diingat.
              </AlertDescription>
            </Alert>

            <Button
              type="submit"
              className="w-full bg-indigo-600 text-white hover:bg-indigo-700"
              disabled={loading || !passwordsMatch || passwordStrength.score < 40}
            >
              {loading ? 'Memproses...' : 'Reset Password'}
            </Button>

            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push('/student')}
              type="button"
            >
              Kembali ke Login
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

// Main component that wraps the form in a Suspense boundary
export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<LoadingState />}>
      <ResetPasswordForm />
    </Suspense>
  )
}
