'use client'

import type React from 'react'

import { useState, useEffect, useCallback, Suspense, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'

import { ThemeToggle } from '@/components/theme-toggle'
import { StudentBottomNav } from '@/components/student-bottom-nav'
import { useToast } from '@/components/ui/use-toast'
import {
  User,
  Shield,
  LogOut,
  Phone,
  Mail,
  CheckCircle,
  Book,
  School,
  Lock,
  ChevronRight,
  Edit,
  ArrowLeft,
  Info,
  AlertTriangle,
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'

interface Student {
  id: number
  uniqueCode: string
  googleEmail: string
  nis: string | null
  name: string
  whatsapp: string | null
  classId: number | null
  className?: string
  createdAt: string
  updatedAt: string | null
  email: string
  hasPassword: boolean
}

/**
 * Format a phone number to ensure it starts with country code (62)
 * @param phoneNumber The phone number to format
 * @returns Formatted phone number
 */
function formatPhoneNumber(phoneNumber: string): string {
  if (!phoneNumber) return ''

  // Remove any non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '')

  // Remove leading zeros if any
  cleaned = cleaned.replace(/^0+/, '')

  // If it doesn't start with 62, add it
  if (!cleaned.startsWith('62')) {
    cleaned = `62${cleaned}`
  }

  return cleaned
}

// Loading fallback component
function ProfileLoading() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-slate-50 dark:bg-slate-900">
      <div className="flex flex-col items-center gap-4">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
        <p className="text-slate-600 dark:text-slate-300">Memuat profil...</p>
      </div>
    </div>
  )
}

// Custom phone input component with better validation
function PhoneInput({
  value,
  onChange,
  disabled = false,
}: {
  value: string
  onChange: (value: string) => void
  disabled?: boolean
}) {
  const inputRef = useRef<HTMLInputElement>(null)

  // Extract the part after the prefix for display
  const displayValue = value?.startsWith('62')
    ? value.substring(2) // If already has 62 prefix, show only the rest
    : value?.startsWith('+62')
      ? value.substring(3) // If has +62 prefix, show only the rest
      : value || '' // Otherwise show as is

  // Handle input change, ensuring format
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value

    // Only allow digits
    const digitsOnly = input.replace(/\D/g, '')

    // Full value with prefix (without + for API)
    const fullValue = `62${digitsOnly}`

    onChange(fullValue)
  }

  const handleClick = () => {
    // Focus the input when the container is clicked
    inputRef.current?.focus()
  }

  return (
    <div
      className={cn(
        'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background',
        'focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
        disabled ? 'cursor-not-allowed opacity-50' : 'cursor-text'
      )}
      onClick={handleClick}
    >
      <span className="flex items-center text-muted-foreground">+62</span>
      <input
        ref={inputRef}
        type="tel"
        value={displayValue}
        onChange={handleInputChange}
        disabled={disabled}
        className="flex-1 border-0 bg-transparent p-0 pl-1 outline-none focus:outline-none focus:ring-0"
        placeholder="812xxxxxxx"
      />
    </div>
  )
}

// Component that uses useSearchParams
function ProfileContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [student, setStudent] = useState<Student | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    whatsapp: '',
    nis: '',
    email: '',
  })
  const [whatsappVerified, setWhatsappVerified] = useState(false)
  const [showOtpDialog, setShowOtpDialog] = useState(false)
  const [otpStep, setOtpStep] = useState<'phone' | 'otp'>('phone')
  const [otp, setOtp] = useState('')
  const [verifyingOtp, setVerifyingOtp] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [checkingWhatsapp, setCheckingWhatsapp] = useState(false)
  const [isWhatsappAvailable, setIsWhatsappAvailable] = useState<boolean | null>(null)
  const [whatsappErrorMessage, setWhatsappErrorMessage] = useState('')
  const [whatsappDebounceTimeout, setWhatsappDebounceTimeout] = useState<NodeJS.Timeout | null>(
    null
  )
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  // State for password change
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [changingPassword, setChangingPassword] = useState(false)
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [showEditProfileDialog, setShowEditProfileDialog] = useState(false)

  // Add state for confirmation dialog
  const [showRemoveWhatsAppDialog, setShowRemoveWhatsAppDialog] = useState(false)

  // Add state for rate limiting
  const [rateLimitRemaining, setRateLimitRemaining] = useState<number | null>(null)
  const [rateLimitTimer, setRateLimitTimer] = useState<number | null>(null)
  const rateLimitIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Helper function to validate email format
  const isValidEmail = (email: string): boolean => {
    // Basic email regex pattern
    const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
    return emailPattern.test(email)
  }

  // Effect to update formData when dialog opens
  useEffect(() => {
    // If the dialog is opening and we have student data
    if (showEditProfileDialog && student) {
      
      setFormData({
        name: student.name || '',
        whatsapp: student.whatsapp || '',
        nis: student.nis || '',
        email: student.googleEmail || student.email || '',
      })
    }
  }, [showEditProfileDialog, student])

  // Check if we should prompt for WhatsApp verification
  useEffect(() => {
    const promptWhatsapp = searchParams.get('prompt') === 'whatsapp'
    if (promptWhatsapp) {
      setShowOtpDialog(true)
    }
  }, [searchParams])

  // Add a specific effect to keep whatsappVerified in sync with student data
  useEffect(() => {
    if (student) {
      const hasWhatsapp = !!student.whatsapp
      
      
      setWhatsappVerified(hasWhatsapp)
    }
  }, [student])

  // Debug useEffect for formData changes
  useEffect(() => {
    
  }, [formData])

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/student/check-auth', {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
          },
        })

        if (!response.ok) {
          // If not authenticated, redirect to login page
          
          router.push('/student')
        }
      } catch (error) {
        console.error('Auth check error:', error)
        router.push('/student')
      }
    }

    checkAuth()
  }, [router])

  // Fungsi untuk melakukan fetch dengan timeout dan retry
  const fetchWithRetry = async (
    url: string,
    options: RequestInit = {},
    maxRetries = 2,
    timeout = 10000
  ) => {
    let retries = 0

    while (retries <= maxRetries) {
      try {
        // Buat AbortController untuk timeout
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout)

        const fetchOptions = {
          ...options,
          signal: controller.signal,
        }

        // Lakukan fetch request
        const response = await fetch(url, fetchOptions)

        // Clear timeout jika fetch berhasil
        clearTimeout(timeoutId)

        return response
      } catch (error) {
        retries++
        

        // Jika sudah mencapai batas retry, lempar error
        if (retries > maxRetries) {
          throw error
        }

        // Tunggu sebelum mencoba lagi (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, 1000 * retries))
      }
    }

    // Fallback jika semua retry gagal (seharusnya tidak pernah sampai sini)
    throw new Error('All fetch attempts failed')
  }

  // Fetch student profile
  useEffect(() => {
    const fetchProfile = async () => {
      try {
        setIsLoading(true)
        

        // Generate a unique cache-busting query parameter
        const cacheBuster = `t=${Date.now()}&_=${Math.random().toString(36).substring(2, 15)}`

        // Gunakan fetchWithRetry untuk menangani koneksi yang tidak stabil
        const response = await fetchWithRetry(
          `/api/student/profile?${cacheBuster}`,
          {
            headers: {
              // Tambahkan cache control untuk mencegah caching
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
              'X-Cache-Bust': Date.now().toString(), // Additional cache-busting header
            },
          },
          2,
          10000
        ) // 2 retries, 10 detik timeout

        if (!response.ok) {
          if (response.status === 401) {
            // Unauthorized, redirect to login
            
            router.push('/student')
            return
          }
          throw new Error(`Failed to fetch profile (${response.status})`)
        }

        let data
        try {
          const responseText = await response.text()
          
          data = JSON.parse(responseText)
        } catch (parseError) {
          console.error('Error parsing profile response:', parseError)
          throw new Error('Failed to parse server response')
        }

        if (data && data.student) {
          // First update the student state
          setStudent(data.student)

          // Then update form data based on the received data
          const newFormData = {
            name: data.student.name || '',
            whatsapp: data.student.whatsapp || '',
            nis: data.student.nis || '',
            email: data.student.googleEmail || data.student.email || '',
          }
          
          setFormData(newFormData)

          // Set WhatsApp verification status based on presence of whatsapp field
          const isWhatsAppVerified = !!data.student.whatsapp
          
          setWhatsappVerified(isWhatsAppVerified)

          // Force re-render by updating a dummy state
          setRefreshTrigger(prev => prev)
        } else {
          console.error('Invalid profile data received:', data)
          toast({
            title: 'Error',
            description: 'Invalid profile data received. Please try again.',
            variant: 'destructive',
          })
        }
      } catch (error) {
        console.error('Error fetching profile:', error)

        // Pesan error yang lebih spesifik berdasarkan jenis error
        let errorMessage = 'Failed to load profile. Please try again.'

        if (error instanceof TypeError && error.message.includes('fetch')) {
          errorMessage = 'Koneksi terputus. Periksa koneksi internet Anda dan coba lagi.'
        } else if (error instanceof DOMException && error.name === 'AbortError') {
          errorMessage = 'Permintaan timeout. Server tidak merespons, coba lagi nanti.'
        } else if (error instanceof Error) {
          errorMessage = error.message
        }

        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchProfile()
  }, [router, refreshTrigger])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    // Update the formData with the new value
    setFormData({
      ...formData,
      [name]: value,
    })
  }

  // Tambahkan state untuk mencegah multiple submit
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Fungsi debounce untuk mencegah multiple submit dalam waktu singkat
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout

    return function executedFunction(...args: any[]) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }

      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  }

  // Modify the debouncedSubmit function to handle WhatsApp changes properly
  const debouncedSubmit = useCallback(
    debounce(async (e: React.FormEvent) => {
      if (isSubmitting) return // Prevent multiple submissions

      setIsSubmitting(true)
      setLoading(true)

      try {
        // Clean up form data - only send fields that have changed
        const payload: any = {}
        let hasChanges = false

        if (student?.nis !== formData.nis) {
          payload.nis = formData.nis || null
          hasChanges = true
        }

        // WhatsApp changes are now handled separately, not through the profile form

        if (student?.googleEmail !== formData.email) {
          // Validate email format before sending
          if (formData.email && !isValidEmail(formData.email)) {
            setIsSubmitting(false)
            setLoading(false)
            toast({
              title: 'Format Email Tidak Valid',
              description: 'Masukkan format email yang valid, contoh: <EMAIL>',
              variant: 'destructive',
            })
            return
          }
          payload.googleEmail = formData.email || null
          hasChanges = true
        }

        if (!hasChanges) {
          setIsSubmitting(false)
          setLoading(false)
          toast({
            title: 'Tidak Ada Perubahan',
            description: 'Tidak ada informasi yang diubah untuk disimpan.',
          })
          return
        }

        

        // Send update to API
        const response = await fetchWithRetry(
          '/api/student/profile',
          {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              Pragma: 'no-cache',
            },
            body: JSON.stringify(payload),
          },
          1,
          5000
        ) // 1 retry, 5 second timeout

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to update profile')
        }

        // Refresh student data
        const updatedData = await response.json()
        if (updatedData && updatedData.student) {
          setStudent(updatedData.student)
          setFormData({
            name: updatedData.student.name || '',
            whatsapp: updatedData.student.whatsapp || '',
            nis: updatedData.student.nis || '',
            email: updatedData.student.googleEmail || '',
          })
          setWhatsappVerified(!!updatedData.student.whatsapp)
        }

        toast({
          title: 'Profil Diperbarui',
          description: 'Informasi profil Anda telah berhasil diperbarui.',
        })

        // Close the edit profile dialog after successful submission
        setShowEditProfileDialog(false)

        // Increment refreshTrigger
        setRefreshTrigger(prevTrigger => prevTrigger + 1)
      } catch (error) {
        console.error('Error updating profile:', error)
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Gagal memperbarui profil',
          variant: 'destructive',
        })
      } finally {
        setIsSubmitting(false)
        setLoading(false)
      }
    }, 500),
    [student, formData, whatsappVerified, isSubmitting, toast]
  )

  // Wrapper function untuk handleSubmit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!isSubmitting) {
      debouncedSubmit(e)
    } else {
      
    }
  }

  const handleLogout = async () => {
    try {
      // Gunakan fetchWithRetry untuk menangani koneksi yang tidak stabil
      const response = await fetchWithRetry(
        '/api/auth/logout?role=student',
        {
          method: 'POST',
          headers: {
            // Tambahkan cache control untuk mencegah caching
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
        },
        1,
        5000
      ) // 1 retry, 5 detik timeout

      if (!response.ok) {
        throw new Error(`Failed to logout (${response.status})`)
      }

      toast({
        title: 'Logout berhasil',
        description: 'Anda telah keluar dari akun',
      })
      router.push('/student')
    } catch (error) {
      console.error('Error logging out:', error)

      // Pesan error yang lebih spesifik berdasarkan jenis error
      let errorMessage = 'Failed to logout. Please try again.'

      if (error instanceof TypeError && error.message.includes('fetch')) {
        errorMessage = 'Koneksi terputus. Periksa koneksi internet Anda dan coba lagi.'
      } else if (error instanceof DOMException && error.name === 'AbortError') {
        errorMessage = 'Permintaan timeout. Server tidak merespons, coba lagi nanti.'
      } else if (error instanceof Error) {
        errorMessage = error.message
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    }
  }

  // Add a new effect to check WhatsApp availability when it changes
  useEffect(() => {
    // Only check when in the phone step of OTP verification
    if (otpStep !== 'phone' || !showOtpDialog) return

    setIsWhatsappAvailable(null)
    setWhatsappErrorMessage('')

    // Only check if the whatsapp number is valid
    if (formData.whatsapp && formData.whatsapp.length >= 11) {
      // Clear previous timeout
      if (whatsappDebounceTimeout) {
        clearTimeout(whatsappDebounceTimeout)
      }

      // Set a small delay to avoid checking on every keystroke
      const timeout = setTimeout(() => {
        checkWhatsappAvailability(formData.whatsapp)
      }, 500)

      setWhatsappDebounceTimeout(timeout)
    }

    return () => {
      if (whatsappDebounceTimeout) {
        clearTimeout(whatsappDebounceTimeout)
      }
    }
  }, [formData.whatsapp, otpStep, showOtpDialog])

  // Add function to check WhatsApp availability
  const checkWhatsappAvailability = async (phoneNumber: string) => {
    if (!phoneNumber) return

    setCheckingWhatsapp(true)

    try {
      // Ensure the phone number has the correct format for the API
      let formattedPhone = phoneNumber
      if (!formattedPhone.startsWith('62')) {
        formattedPhone = `62${formattedPhone}`
      }

      const response = await fetchWithRetry(
        '/api/student/whatsapp/check',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
          },
          body: JSON.stringify({ whatsapp: formattedPhone }),
          credentials: 'include',
        },
        1,
        5000
      )

      if (response.ok) {
        const data = await response.json()
        setIsWhatsappAvailable(data.available)

        if (!data.available) {
          setWhatsappErrorMessage('Nomor WhatsApp ini sudah terdaftar pada akun lain')
        }
      } else {
        console.error('Failed to check WhatsApp availability:', response.status)
        // Don't block the user if check fails
        setIsWhatsappAvailable(true)
      }
    } catch (error) {
      console.error('Error checking WhatsApp availability:', error)
      // Don't block the user if check fails
      setIsWhatsappAvailable(true)
    } finally {
      setCheckingWhatsapp(false)
    }
  }

  // Add effect to handle the countdown timer for rate limit
  useEffect(() => {
    if (rateLimitTimer && rateLimitTimer > 0) {
      rateLimitIntervalRef.current = setInterval(() => {
        setRateLimitTimer(prev => {
          if (prev && prev > 0) {
            return prev - 1
          }
          return null
        })
      }, 1000)
    } else if (rateLimitTimer === 0) {
      setRateLimitTimer(null)
      setRateLimitRemaining(null)
    }

    return () => {
      if (rateLimitIntervalRef.current) {
        clearInterval(rateLimitIntervalRef.current)
      }
    }
  }, [rateLimitTimer])

  // Function to format time in MM:SS format
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // Modify handleSendOtp to better handle rate limiting
  const handleSendOtp = async () => {
    setVerifyingOtp(true)
    setLoading(true)

    // Ensure the phone number has the correct format for the API
    const phoneNumber = formData.whatsapp || ''

    // Validate phone number format first
    if (!phoneNumber.startsWith('62') || phoneNumber.length < 11) {
      toast({
        title: 'Format Tidak Valid',
        description: 'Nomor WhatsApp harus dimulai dengan 62 dan minimal 11 karakter',
        variant: 'destructive',
      })
      setVerifyingOtp(false)
      setLoading(false)
      return
    }

    // Final check for availability
    if (isWhatsappAvailable === false) {
      toast({
        title: 'Nomor Sudah Digunakan',
        description: 'Nomor WhatsApp ini sudah terdaftar pada akun lain',
        variant: 'destructive',
      })
      setVerifyingOtp(false)
      setLoading(false)
      return
    }

    // Make sure it starts with 62 (not +62)
    const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber.substring(1) : phoneNumber

    

    try {
      // Gunakan fetchWithRetry untuk menangani koneksi yang tidak stabil
      const response = await fetchWithRetry(
        '/api/student/whatsapp/send-otp',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Tambahkan cache control untuk mencegah caching
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
          body: JSON.stringify({
            whatsapp: formattedPhone, // Use the formatted phone number
          }),
          credentials: 'include', // Make sure cookies are sent with the request
        },
        1,
        15000
      )

      

      if (!response.ok) {
        let errorData
        let errorText = ''

        try {
          // Try to get response as text first for debugging
          errorText = await response.text()
          

          // Try to parse as JSON if possible
          if (errorText) {
            try {
              errorData = JSON.parse(errorText)
            } catch (jsonError) {
              
              errorData = { error: errorText }
            }
          } else {
            errorData = { error: 'No response body' }
          }
        } catch (parseError) {
          console.error('Error parsing error response:', parseError)
          errorData = { error: 'Unknown error', status: response.status }
        }

        console.error('Failed to send OTP:', errorData)

        // Extract error details if available
        const errorMessage =
          errorData?.details || errorData?.error || 'Gagal mengirim OTP. Silakan coba lagi.'

        if (response.status === 401) {
          // Authentication error - check if we need to re-authenticate
          
          toast({
            title: 'Autentikasi Gagal',
            description: 'Silakan coba me-refresh halaman ini',
            variant: 'destructive',
          })
        } else if (response.status === 400) {
          toast({
            title: 'Format Tidak Valid',
            description: errorMessage,
            variant: 'destructive',
          })
        } else if (response.status === 429) {
          // Rate limit error - show how long to wait
          // Default to 60 minutes if no specific time is provided
          const waitTimeInMinutes = 60
          const waitTimeInSeconds = waitTimeInMinutes * 60

          setRateLimitRemaining(5) // Max attempts allowed (changed from 3 to 5)
          setRateLimitTimer(waitTimeInSeconds)

          toast({
            title: 'Batas Permintaan Terlampaui',
            description: `Anda telah mencapai batas maksimum 5 permintaan OTP per jam. Silakan coba lagi nanti.`,
            variant: 'destructive',
          })
        } else if (response.status >= 500) {
          toast({
            title: 'Layanan Tidak Tersedia',
            description: errorMessage,
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Pengiriman OTP Gagal',
            description: errorMessage,
            variant: 'destructive',
          })
        }

        throw new Error(errorMessage)
      }

      setOtpStep('otp')
      toast({
        title: 'OTP dikirim',
        description: 'Silakan periksa WhatsApp Anda untuk kode OTP',
      })
    } catch (error) {
      console.error('Error sending OTP:', error)

      // We've already shown the toast in the response handling section,
      // so we only need to handle network errors here
      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast({
          title: 'Koneksi Error',
          description: 'Koneksi terputus. Periksa koneksi internet Anda dan coba lagi.',
          variant: 'destructive',
        })
      } else if (error instanceof DOMException && error.name === 'AbortError') {
        toast({
          title: 'Permintaan Timeout',
          description: 'Server tidak merespons, coba lagi nanti.',
          variant: 'destructive',
        })
      }
      // No need for an else case, we've already handled specific error toasts above
    } finally {
      setVerifyingOtp(false)
      setLoading(false)
    }
  }

  // Update the handleVerifyOtp function to handle the case when changing a number
  const handleVerifyOtp = async () => {
    if (verifyingOtp) return
    setVerifyingOtp(true)

    try {
      const phoneNumber = formData.whatsapp || ''
      const originalNumber = student?.whatsapp

      // Format phone number for API
      let formattedPhone = formatPhoneNumber(phoneNumber)

      

      // Gunakan fetchWithRetry untuk menangani koneksi yang tidak stabil
      const response = await fetchWithRetry(
        '/api/student/whatsapp/verify-otp',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // Tambahkan cache control untuk mencegah caching
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
          body: JSON.stringify({
            whatsapp: formattedPhone, // Use the formatted phone number
            otp: otp,
          }),
          credentials: 'include', // Make sure cookies are sent with the request
        },
        1,
        10000
      ) // 1 retry, 10 detik timeout

      

      if (!response.ok) {
        let errorData
        let errorText = ''

        try {
          // Try to get response as text first for debugging
          errorText = await response.text()
          

          // Try to parse as JSON if possible
          if (errorText) {
            try {
              errorData = JSON.parse(errorText)
            } catch (jsonError) {
              
              errorData = { error: errorText }
            }
          } else {
            errorData = { error: 'No response body' }
          }
        } catch (parseError) {
          console.error('Error parsing error response:', parseError)
          errorData = { error: 'Unknown error', status: response.status }
        }

        console.error('Failed to verify OTP:', errorData)

        // Extract error details if available
        const errorMessage =
          errorData?.details || errorData?.error || 'Gagal verifikasi OTP. Silakan coba lagi.'

        if (response.status === 401) {
          // Authentication error - check if we need to re-authenticate
          
          toast({
            title: 'Autentikasi Gagal',
            description: 'Silakan coba me-refresh halaman ini',
            variant: 'destructive',
          })
          return
        } else if (response.status === 400) {
          toast({
            title: 'OTP Tidak Valid',
            description: errorMessage,
            variant: 'destructive',
          })
        } else if (response.status === 409) {
          // Handle conflict (duplicate WhatsApp number)
          toast({
            title: 'Nomor Sudah Digunakan',
            description: 'Nomor WhatsApp ini sudah terdaftar pada akun lain',
            variant: 'destructive',
          })
          setOtpStep('phone') // Go back to phone input step
          setWhatsappErrorMessage('Nomor WhatsApp ini sudah terdaftar pada akun lain')
          setIsWhatsappAvailable(false)
        } else if (response.status === 429) {
          toast({
            title: 'Terlalu Banyak Percobaan',
            description: errorMessage,
            variant: 'destructive',
          })
        } else if (response.status >= 500) {
          toast({
            title: 'Layanan Tidak Tersedia',
            description: errorMessage,
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Verifikasi Gagal',
            description: errorMessage,
            variant: 'destructive',
          })
        }

        throw new Error(errorMessage)
      }

      // Parse successful response
      const data = await response.json()
      

      // Update UI state with the verified WhatsApp
      
      setWhatsappVerified(true)
      setShowOtpDialog(false) // Close the dialog

      // Update formData with the now-verified WhatsApp
      if (student) {
        const updatedStudent = {
          ...student,
          whatsapp: formattedPhone,
        }
        
        setStudent(updatedStudent)

        // Also update the form data to reflect the change
        setFormData(prev => ({
          ...prev,
          whatsapp: formattedPhone,
        }))
      }

      // Show success message
      toast({
        title: originalNumber ? 'Nomor WhatsApp Diperbarui' : 'WhatsApp Terverifikasi',
        description: originalNumber
          ? 'Nomor WhatsApp Anda berhasil diperbarui'
          : 'Nomor WhatsApp Anda telah berhasil diverifikasi',
      })

      // Refresh the data to ensure UI is updated
      setRefreshTrigger(prevTrigger => prevTrigger + 1)
    } catch (error) {
      console.error('Error verifying OTP:', error)
      // Error toasts are already shown in the error-handling code above
    } finally {
      setVerifyingOtp(false)
    }
  }

  // Handle password change
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault()
    

    // Additional validation just to be safe
    if (!currentPassword) {
      
      toast({
        title: 'Error',
        description: 'Kata sandi saat ini harus diisi',
        variant: 'destructive',
      })
      return
    }

    // Validate password fields
    if (newPassword !== confirmPassword) {
      
      toast({
        title: 'Error',
        description: 'Konfirmasi kata sandi tidak cocok.',
        variant: 'destructive',
      })
      return
    }

    if (newPassword.length < 6) {
      
      toast({
        title: 'Error',
        description: 'Kata sandi baru harus minimal 6 karakter.',
        variant: 'destructive',
      })
      return
    }

    try {
      setChangingPassword(true)

      // Call the password change API
      const response = await fetchWithRetry(
        '/api/student/password/change',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
          body: JSON.stringify({
            currentPassword,
            newPassword,
          }),
          credentials: 'include', // Make sure cookies are sent for authentication
        },
        1,
        5000
      ) // 1 retry, 5 second timeout

      

      if (!response.ok) {
        let errorData
        try {
          errorData = await response.json()
          console.error('Password change error response:', errorData)
        } catch (jsonError) {
          console.error('Failed to parse error response:', await response.text())
          errorData = { error: 'Failed to parse error response' }
        }

        if (response.status === 401 && errorData.error?.includes('incorrect')) {
          
          toast({
            title: 'Error',
            description: 'Kata sandi saat ini tidak sesuai.',
            variant: 'destructive',
          })
          return
        } else {
          throw new Error(errorData.error || 'Failed to change password')
        }
      } else {
        // Log success response
        const responseData = await response.json()
        

        // Reset the form fields
        setCurrentPassword('')
        setNewPassword('')
        setConfirmPassword('')

        toast({
          title: 'Sukses',
          description: 'Kata sandi berhasil diubah.',
        })

        // Close the password dialog
        setShowPasswordDialog(false)

        // Increment refreshTrigger to fetch latest data
        setRefreshTrigger(prevTrigger => prevTrigger + 1)
      }
    } catch (error) {
      console.error('Password change error details:', error)

      let errorMessage = 'Gagal mengubah kata sandi.'

      if (error instanceof Error) {
        if (error.message.includes('incorrect')) {
          errorMessage = 'Kata sandi saat ini tidak sesuai.'
        } else {
          errorMessage = error.message
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setChangingPassword(false)
    }
  }

  // Add a function to handle initiating WhatsApp number change
  const handleChangeWhatsApp = () => {
    // Reset the WhatsApp field in form to empty to allow entering a new number
    setFormData({
      ...formData,
      whatsapp: '',
    })

    // Open OTP dialog to verify new number
    setOtpStep('phone')
    setShowOtpDialog(true)
  }

  // Add function to handle removing WhatsApp
  const handleRemoveWhatsApp = async () => {
    if (isSubmitting) return

    setIsSubmitting(true)
    setLoading(true)

    try {
      const response = await fetchWithRetry(
        '/api/student/profile',
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
          body: JSON.stringify({ whatsapp: null }),
        },
        1,
        5000
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to remove WhatsApp number')
      }

      // Refresh student data
      const updatedData = await response.json()
      if (updatedData && updatedData.student) {
        setStudent(updatedData.student)
        setFormData({
          ...formData,
          whatsapp: '',
        })
        setWhatsappVerified(false)
      }

      toast({
        title: 'WhatsApp Dihapus',
        description: 'Nomor WhatsApp Anda telah dihapus dari profil',
      })

      // Close dialogs
      setShowRemoveWhatsAppDialog(false)
      setShowEditProfileDialog(false)

      // Increment refreshTrigger
      setRefreshTrigger(prevTrigger => prevTrigger + 1)
    } catch (error) {
      console.error('Error removing WhatsApp:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Gagal menghapus nomor WhatsApp',
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
      setLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-slate-50 dark:bg-slate-900">
        <div className="flex flex-col items-center gap-4">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
          <p className="text-slate-600 dark:text-slate-300">Memuat profil...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Header - Improved for mobile */}
      <header className="sticky top-0 z-10 flex items-center justify-between border-b border-slate-200 bg-white px-3 py-3 dark:border-slate-700 dark:bg-slate-800 sm:px-4 sm:py-4">
        <h1 className="text-lg font-semibold text-slate-800 dark:text-slate-100 sm:text-xl">
          Profil Saya
        </h1>
        <ThemeToggle />
      </header>

      <main className="container mx-auto max-w-md px-4 pb-20">
        {/* Profile Header Card */}
        <Card className="mt-6 overflow-hidden">
          <CardContent className="p-6">
            <div className="flex flex-col items-center">
              <div className="flex h-20 w-20 items-center justify-center rounded-full bg-indigo-100 text-indigo-600 dark:bg-indigo-900 dark:text-indigo-300">
                <User className="h-10 w-10" />
              </div>
              <h2 className="mt-4 text-xl font-semibold text-slate-800 dark:text-slate-100">
                {student?.name || formData.name}
              </h2>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {student?.className || 'Belum diatur'}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Main Info Card */}
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="text-lg">Informasi Siswa</CardTitle>
            <CardDescription>Detail informasi akun siswa</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-1">
              <Label className="text-xs text-slate-500 dark:text-slate-400">NIS</Label>
              <p className="text-sm font-medium">{student?.nis || 'Belum diatur'}</p>
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-slate-500 dark:text-slate-400">Email</Label>
              <p className="text-sm font-medium">
                {student?.googleEmail || student?.email || 'Belum diatur'}
              </p>
            </div>

            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <Label className="text-xs text-slate-500 dark:text-slate-400">WhatsApp</Label>
                {student?.whatsapp && (
                  <span className="inline-flex items-center text-xs text-green-600 dark:text-green-400">
                    <CheckCircle className="mr-1 h-3 w-3" /> Terverifikasi
                  </span>
                )}
              </div>
              {/* Debug WhatsApp status */}
              {student && (
                <div className="hidden">
                  <p>Student WhatsApp (Debug): {student.whatsapp ? student.whatsapp : 'Not set'}</p>
                  <p>WhatsAppVerified state: {whatsappVerified ? 'true' : 'false'}</p>
                </div>
              )}
              {student?.whatsapp ? (
                <div className="flex items-center">
                  <Phone className="mr-2 h-4 w-4 text-indigo-500" />
                  <p className="text-sm font-medium">
                    {student?.whatsapp?.replace('62', '+62') || ''}
                  </p>
                </div>
              ) : (
                <p className="text-sm font-medium text-amber-600 dark:text-amber-400">
                  Belum diverifikasi
                </p>
              )}
              <div className="flex space-x-2 pt-2">
                {student?.whatsapp ? (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleChangeWhatsApp}
                      className="h-8"
                    >
                      <Edit className="mr-1 h-3 w-3" /> Ganti
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowRemoveWhatsAppDialog(true)}
                      className="h-8 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 dark:border-red-800 dark:hover:bg-red-950"
                    >
                      Hapus
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => {
                      setOtpStep('phone')
                      setShowOtpDialog(true)
                    }}
                    className="h-8"
                  >
                    <Phone className="mr-1 h-3 w-3" /> Verifikasi WhatsApp
                  </Button>
                )}
              </div>
            </div>

            <div className="space-y-1">
              <Label className="text-xs text-slate-500 dark:text-slate-400">Kode Unik</Label>
              <p className="text-sm font-medium">{student?.uniqueCode}</p>
            </div>
          </CardContent>
        </Card>

        {/* Actions Card */}
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="text-lg">Aksi</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => setShowEditProfileDialog(true)}
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit Profil
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start text-left"
              onClick={() => setShowPasswordDialog(true)}
            >
              <Lock className="mr-2 h-4 w-4" />
              Ubah Kata Sandi
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start text-left text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-900/10 dark:hover:text-red-300"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              Keluar
            </Button>
          </CardContent>
        </Card>
      </main>

      <StudentBottomNav activeTab="profile" />

      {/* Edit Profile Dialog */}
      <Dialog open={showEditProfileDialog} onOpenChange={setShowEditProfileDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Profil</DialogTitle>
            <DialogDescription>Perbarui informasi profil Anda di sini.</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Nama</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  disabled
                />
              </div>

              {/* NIS field */}
              <div className="grid gap-2">
                <Label htmlFor="nis">Nomor Induk Siswa</Label>
                <Input
                  id="nis"
                  name="nis"
                  placeholder="Masukkan NIS Anda"
                  value={formData.nis}
                  onChange={handleChange}
                />
              </div>

              {/* Email field */}
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                />
                {formData.email && !isValidEmail(formData.email) && (
                  <p className="mt-1 text-xs text-red-500">
                    <AlertTriangle className="mr-1 inline-block h-3 w-3" />
                    Format email tidak valid
                  </p>
                )}
              </div>

              {/* Class information (read-only) */}
              <div className="grid gap-2">
                <Label>Kelas</Label>
                <div className="rounded-md bg-slate-100 p-2 text-sm dark:bg-slate-700">
                  {student?.className || 'Belum diatur'}
                </div>
                <p className="text-xs text-slate-500">
                  Jika terdapat kesalahan pada kelas, silakan hubungi admin untuk perbaikan.
                </p>
              </div>
            </div>

            {/* Submit button */}
            <DialogFooter>
              <Button
                type="submit"
                disabled={
                  loading ||
                  isSubmitting ||
                  (formData.email ? !isValidEmail(formData.email) : false)
                }
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Menyimpan...
                  </>
                ) : (
                  'Simpan Perubahan'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* WhatsApp OTP Dialog */}
      <Dialog
        open={showOtpDialog}
        onOpenChange={isOpen => {
          if (!verifyingOtp) {
            setShowOtpDialog(isOpen)
            if (!isOpen) {
              // Reset states when closing the dialog
              setOtpStep('phone')
              setOtp('')
              setIsWhatsappAvailable(null)
              setWhatsappErrorMessage('')
            }
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {otpStep === 'phone'
                ? student?.whatsapp
                  ? 'Ganti Nomor WhatsApp'
                  : 'Verifikasi Nomor WhatsApp'
                : 'Masukkan Kode OTP'}
            </DialogTitle>
            <DialogDescription>
              {otpStep === 'phone'
                ? student?.whatsapp
                  ? 'Masukkan nomor WhatsApp baru Anda untuk menerima kode OTP verifikasi.'
                  : 'Masukkan nomor WhatsApp Anda untuk menerima kode OTP verifikasi.'
                : 'Kode OTP telah dikirim ke WhatsApp Anda. Masukkan kode OTP untuk verifikasi.'}
            </DialogDescription>
          </DialogHeader>

          {otpStep === 'phone' ? (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="whatsapp-input">Nomor WhatsApp</Label>
                <div className="relative">
                  <PhoneInput
                    value={formData.whatsapp || student?.whatsapp || ''}
                    onChange={value =>
                      setFormData({
                        ...formData,
                        whatsapp: value,
                      })
                    }
                    disabled={loading || verifyingOtp}
                  />
                  {checkingWhatsapp && (
                    <div className="absolute right-3 top-3">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-slate-500 border-t-transparent"></div>
                    </div>
                  )}
                </div>
                {whatsappErrorMessage && (
                  <p className="flex items-center gap-1 text-xs text-red-500">
                    <AlertTriangle className="h-3 w-3" />
                    {whatsappErrorMessage}
                  </p>
                )}
                <p className="text-xs text-slate-500">Format: 8123456789 (tanpa awalan 0)</p>
              </div>

              {rateLimitTimer !== null && rateLimitTimer > 0 && (
                <div className="rounded-md bg-amber-50 p-3 dark:bg-amber-900/20">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <AlertTriangle className="h-5 w-5 text-amber-500" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                        Batas Permintaan OTP Terlampaui
                      </h3>
                      <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                        <p>Anda telah mencapai batas maksimum 5 permintaan OTP per jam.</p>
                        <p className="mt-1">
                          Silakan coba lagi dalam:{' '}
                          <span className="font-mono font-medium">
                            {formatTime(rateLimitTimer)}
                          </span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <Button
                className="w-full bg-indigo-600 hover:bg-indigo-700"
                onClick={handleSendOtp}
                disabled={
                  loading ||
                  verifyingOtp ||
                  !formData.whatsapp ||
                  formData.whatsapp.length < 11 ||
                  isWhatsappAvailable === false ||
                  checkingWhatsapp ||
                  rateLimitTimer !== null
                }
              >
                {loading || verifyingOtp ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Mengirim OTP...
                  </>
                ) : checkingWhatsapp ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Memeriksa...
                  </>
                ) : rateLimitTimer !== null ? (
                  'Tunggu cooldown'
                ) : (
                  'Kirim OTP'
                )}
              </Button>
            </div>
          ) : (
            <div className="space-y-4 py-4">
              <div>
                <div className="mb-4 rounded-md bg-blue-50 p-3 dark:bg-blue-900/30">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <CheckCircle className="h-5 w-5 text-blue-400" aria-hidden="true" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        OTP telah dikirim ke WhatsApp {formData.whatsapp?.replace('62', '+62')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="otp-input">Kode OTP</Label>
                <Input
                  id="otp-input"
                  placeholder="Masukkan 6 digit kode OTP"
                  value={otp}
                  onChange={e => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  maxLength={6}
                  disabled={verifyingOtp}
                  className="text-center text-lg tracking-wider"
                />
              </div>
              <div className="flex flex-col gap-2">
                <Button
                  className="bg-indigo-600 hover:bg-indigo-700"
                  onClick={handleVerifyOtp}
                  disabled={verifyingOtp || otp.length !== 6 || loading}
                >
                  {verifyingOtp ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      Memverifikasi...
                    </>
                  ) : (
                    'Verifikasi OTP'
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    if (!verifyingOtp) {
                      setOtpStep('phone')
                      setOtp('')
                    }
                  }}
                  disabled={verifyingOtp}
                >
                  Kembali
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Password Change Dialog */}
      <Dialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ganti Kata Sandi</DialogTitle>
            <DialogDescription>
              Masukkan kata sandi saat ini dan kata sandi baru untuk mengubah kata sandi Anda.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleChangePassword} className="space-y-4 py-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="currentPassword">Kata Sandi Saat Ini</Label>
                <Input
                  id="currentPassword"
                  type="password"
                  value={currentPassword}
                  onChange={e => setCurrentPassword(e.target.value)}
                  required
                />
                {currentPassword === '' && (
                  <p className="mt-1 text-xs text-amber-500">
                    <AlertTriangle className="mr-1 inline-block h-3 w-3" />
                    Kata sandi saat ini diperlukan
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="newPassword">Kata Sandi Baru</Label>
                <Input
                  id="newPassword"
                  type="password"
                  value={newPassword}
                  onChange={e => setNewPassword(e.target.value)}
                  required
                />
                {newPassword && newPassword.length < 6 && (
                  <p className="mt-1 text-xs text-amber-500">
                    <AlertTriangle className="mr-1 inline-block h-3 w-3" />
                    Kata sandi baru minimal 6 karakter
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Konfirmasi Kata Sandi Baru</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={e => setConfirmPassword(e.target.value)}
                  required
                />
                {newPassword !== confirmPassword && confirmPassword && (
                  <p className="mt-1 text-xs text-red-500">Konfirmasi kata sandi tidak cocok.</p>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowPasswordDialog(false)}
                disabled={changingPassword}
              >
                Batal
              </Button>
              <Button
                type="submit"
                disabled={
                  changingPassword ||
                  !currentPassword ||
                  !newPassword ||
                  newPassword.length < 6 ||
                  newPassword !== confirmPassword
                }
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                {changingPassword ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                    Mengubah...
                  </>
                ) : (
                  'Simpan'
                )}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add Confirmation Dialog for removing WhatsApp */}
      <Dialog open={showRemoveWhatsAppDialog} onOpenChange={setShowRemoveWhatsAppDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Hapus Nomor WhatsApp</DialogTitle>
            <DialogDescription>
              Apakah Anda yakin ingin menghapus nomor WhatsApp dari profil Anda?
            </DialogDescription>
          </DialogHeader>

          <div className="mt-2 rounded-md bg-amber-50 p-4 dark:bg-amber-900/20">
            <div className="flex">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-5 w-5 text-amber-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
                  Perhatian
                </h3>
                <div className="mt-2 text-sm text-amber-700 dark:text-amber-300">
                  <p>Jika Anda menghapus nomor WhatsApp, Anda tidak akan menerima:</p>
                  <ul className="mt-1 list-disc pl-5">
                    <li>Notifikasi penting dari sekolah</li>
                    <li>Kode OTP untuk atur ulang kata sandi</li>
                  </ul>
                  <p className="mt-1">Anda dapat menambahkan nomor WhatsApp lagi kapan saja.</p>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowRemoveWhatsAppDialog(false)}
            >
              Batal
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleRemoveWhatsApp}
              disabled={isSubmitting || loading}
            >
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Menghapus...
                </>
              ) : (
                'Hapus Nomor'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Main component that wraps ProfileContent in a Suspense boundary
export default function StudentProfile() {
  return (
    <Suspense fallback={<ProfileLoading />}>
      <ProfileContent />
    </Suspense>
  )
}
