version: '3'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        DATABASE_URL: ${DATABASE_URL}
        REDIS_URL: ${REDIS_URL}
        JWT_SECRET: ${JWT_SECRET}
        NEXTAUTH_URL: ${NEXTAUTH_URL}
        NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
        DOMAIN: libstudio.my.id
        STUDENT_DOMAIN: shalatyuk.libstudio.my.id
        ADMIN_DOMAIN: adminshalat.libstudio.my.id
    restart: always
    ports:
      - '3000:3000'
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - DOMAIN=libstudio.my.id
      - STUDENT_DOMAIN=shalatyuk.libstudio.my.id
      - ADMIN_DOMAIN=adminshalat.libstudio.my.id
      - NODE_ENV=production
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15-alpine
    restart: always
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-shalat_yuk}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'

  redis:
    image: redis:alpine
    restart: always
    volumes:
      - redis_data:/data
    ports:
      - '6379:6379'

volumes:
  postgres_data:
  redis_data:
