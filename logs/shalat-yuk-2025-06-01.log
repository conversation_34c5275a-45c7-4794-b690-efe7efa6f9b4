{"level":"info","message":"API GET /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:27"}
{"level":"info","message":"API GET /api/classes - 200","requestType":"api","responseTime":"800ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:28"}
{"level":"info","message":"API GET /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:30"}
{"level":"info","message":"API GET /api/classes - 200","requestType":"api","responseTime":"85ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:31"}
{"level":"info","message":"API GET /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:33"}
{"level":"info","message":"API GET /api/classes - 200","requestType":"api","responseTime":"104ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:33"}
{"level":"info","message":"API GET /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:33"}
{"level":"info","message":"API GET /api/classes - 200","requestType":"api","responseTime":"37ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:33"}
{"level":"info","message":"API GET /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API HEAD /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API GET /api/classes - 200","requestType":"api","responseTime":"95ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API HEAD /api/classes - 200","requestType":"api","responseTime":"93ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API GET /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API GET /api/classes - 200","requestType":"api","responseTime":"42ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API HEAD /api/classes","requestType":"api","service":"shalat-yuk","timestamp":"2025-06-01 10:33:43"}
{"level":"info","message":"API HEAD /api/classes - 200","requestType":"api","responseTime":"39ms","service":"shalat-yuk","statusCode":200,"timestamp":"2025-06-01 10:33:43"}
