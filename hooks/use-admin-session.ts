'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface AdminSession {
  id: number
  username: string
  name: string
  role?: 'admin' | 'super_admin'
}

export function useAdminSession() {
  const [admin, setAdmin] = useState<AdminSession | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  // Periksa sesi admin saat komponen dimount
  useEffect(() => {
    const checkSession = async () => {
      try {
        // Coba ambil data admin dari API
        const response = await fetch('/api/auth/admin/session', {
          method: 'GET',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            Pragma: 'no-cache',
          },
        })

        if (response.ok) {
          const data = await response.json()
          setAdmin(data.admin)
        } else {
          // Jika tidak ada sesi yang valid, set admin ke null
          setAdmin(null)
        }
      } catch (error) {
        console.error('Error checking admin session:', error)
        setAdmin(null)
      } finally {
        setLoading(false)
      }
    }

    checkSession()
  }, [])

  // Fungsi untuk logout
  const logout = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/auth/logout?role=admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        setAdmin(null)
        router.push('/admin')
      }
    } catch (error) {
      console.error('Error logging out:', error)
    } finally {
      setLoading(false)
    }
  }

  return {
    admin,
    loading,
    isAuthenticated: !!admin,
    logout,
  }
}
