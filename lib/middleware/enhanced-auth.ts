import { NextRequest, NextResponse } from 'next/server'
import { verifyToken, JWTPayload } from '@/lib/utils/auth'
import { getClientIpAddress } from '@/lib/utils/session'
import { SessionUseCases } from '@/lib/domain/usecases/session'
import { RedisSessionRepository } from '@/lib/data/repositories/redis-session-repository'
import { StudentRepository } from '@/lib/data/repositories/student'
import { AdminRepository } from '@/lib/data/repositories/admin'
import { getRedisCache } from '@/lib/data/cache/redis'
import { serverConfig } from '@/lib/config'

// Initialize dependencies
const cache = getRedisCache()
const sessionRepo = new RedisSessionRepository(cache)
const studentRepo = new StudentRepository(cache)
const adminRepo = new AdminRepository(cache)
const sessionUseCases = new SessionUseCases(sessionRepo, studentRepo, adminRepo, cache)

/**
 * Enhanced authentication result
 */
export interface AuthenticationResult {
  id: number
  role: 'student' | 'admin' | 'super_admin'
  sessionId: string
  deviceId: string
  newToken?: string // If token was refreshed
}

/**
 * Enhanced middleware to authenticate requests with session validation
 * @param req The incoming request
 * @param role The expected role ('admin' or 'student')
 * @returns The authenticated user info and session data
 */
export async function authenticateWithSession(
  req: NextRequest,
  role?: 'admin' | 'student'
): Promise<AuthenticationResult> {
  // Get the appropriate auth token
  let authToken: string | undefined = undefined

  if (role === 'admin') {
    authToken = req.cookies.get('admin_auth_token')?.value
  } else if (role === 'student') {
    authToken = req.cookies.get('student_auth_token')?.value
  } else {
    // If no specific role, try to find any auth token
    authToken =
      req.cookies.get('admin_auth_token')?.value || req.cookies.get('student_auth_token')?.value
  }

  if (!authToken) {
    throw new Error('Authentication required')
  }

  let decoded: JWTPayload
  try {
    decoded = verifyToken(authToken, serverConfig.auth.jwtSecret || '')

    if (!decoded || !decoded.id) {
      console.error('Token verification failed: Missing ID in token payload')
      throw new Error('Invalid token')
    }

    // Validate role if specified
    if (role && decoded.role !== role && !(role === 'admin' && decoded.role === 'super_admin')) {
      throw new Error('Insufficient permissions')
    }
  } catch (error) {
    console.error('Token verification error:', error)
    throw new Error('Invalid or expired token')
  }

  // If token has session information, validate the session
  if (decoded.sessionId && decoded.sessionId !== 'legacy') {
    try {
      const sessionValidation = await sessionUseCases.validateAndRefreshSession(
        decoded.sessionId,
        decoded.id
      )

      if (!sessionValidation.isValid) {
        throw new Error('Session is invalid or expired')
      }

      // Return authentication result with optional new token
      return {
        id: decoded.id,
        role: decoded.role,
        sessionId: decoded.sessionId,
        deviceId: decoded.deviceId || 'unknown',
        newToken:
          sessionValidation.newToken === 'REFRESH_NEEDED' ? undefined : sessionValidation.newToken,
      }
    } catch (error) {
      console.error('Session validation error:', error)
      throw new Error('Session validation failed')
    }
  }

  // Fallback for legacy tokens without session info
  return {
    id: decoded.id,
    role: decoded.role,
    sessionId: 'legacy',
    deviceId: decoded.deviceId || 'unknown',
  }
}

/**
 * Middleware specifically for super admin access
 */
export async function authenticateSuperAdmin(req: NextRequest): Promise<AuthenticationResult> {
  const result = await authenticateWithSession(req, 'admin')

  if (result.role !== 'super_admin') {
    throw new Error('Super admin access required')
  }

  return result
}

/**
 * Create response with refreshed token if needed
 */
export function createResponseWithRefreshedToken(
  response: NextResponse,
  authResult: AuthenticationResult,
  role: 'admin' | 'student'
): NextResponse {
  if (authResult.newToken) {
    const cookieName = role === 'admin' ? 'admin_auth_token' : 'student_auth_token'

    response.cookies.set(cookieName, authResult.newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60, // 1 hour
      path: '/',
    })
  }

  return response
}

/**
 * Validate session from request headers (for API calls)
 */
export async function validateSessionFromHeaders(req: NextRequest): Promise<{
  isValid: boolean
  userId?: number
  role?: string
  sessionId?: string
  error?: string
}> {
  const authHeader = req.headers.get('authorization')

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return {
      isValid: false,
      error: 'Missing or invalid authorization header',
    }
  }

  const token = authHeader.substring(7) // Remove 'Bearer ' prefix

  try {
    const decoded = verifyToken(token, serverConfig.auth.jwtSecret || '')

    if (decoded.sessionId) {
      const sessionValidation = await sessionUseCases.validateSession(decoded.sessionId, true)

      if (!sessionValidation.isValid) {
        return {
          isValid: false,
          error: 'Session is invalid or expired',
        }
      }

      return {
        isValid: true,
        userId: decoded.id,
        role: decoded.role,
        sessionId: decoded.sessionId,
      }
    }

    // Legacy token without session
    return {
      isValid: true,
      userId: decoded.id,
      role: decoded.role,
      sessionId: 'legacy',
    }
  } catch (error) {
    return {
      isValid: false,
      error: 'Invalid token',
    }
  }
}

/**
 * Extract device information from request
 */
export function extractDeviceInfo(req: NextRequest): {
  userAgent: string
  ipAddress: string
} {
  const userAgent = req.headers.get('user-agent') || 'Unknown'
  const ipAddress = getClientIpAddress(req.headers)

  return { userAgent, ipAddress }
}

/**
 * Check if request is from same device as session
 */
export async function validateDeviceConsistency(
  req: NextRequest,
  sessionId: string
): Promise<boolean> {
  try {
    const session = await sessionUseCases.getSession(sessionId)

    if (!session) {
      return false
    }

    const { userAgent, ipAddress } = extractDeviceInfo(req)

    // For now, we'll just check if the user agent matches
    // In a production system, you might want more sophisticated device fingerprinting
    return session.userAgent === userAgent
  } catch (error) {
    console.error('Device consistency check failed:', error)
    return false
  }
}

/**
 * Logout helper that clears cookies and invalidates session
 */
export async function logoutUser(
  response: NextResponse,
  sessionId: string,
  userId: number,
  role: 'admin' | 'student'
): Promise<NextResponse> {
  // Invalidate session
  await sessionUseCases.invalidateSession(sessionId)

  // Clear auth cookies
  const cookieName = role === 'admin' ? 'admin_auth_token' : 'student_auth_token'
  const refreshCookieName = role === 'admin' ? 'admin_refresh_token' : 'student_refresh_token'

  response.cookies.set(cookieName, '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0,
    path: '/',
  })

  response.cookies.set(refreshCookieName, '', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 0,
    path: '/',
  })

  return response
}
