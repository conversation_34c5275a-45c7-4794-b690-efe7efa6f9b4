import { eq, and, count } from 'drizzle-orm'
import { db } from '../drizzle/db'
import * as schema from '../drizzle/schema'
import { Student, CreateStudentDTO, UpdateStudentDTO } from '../../domain/entities/student'
import { CacheService } from '../../domain/usecases/auth'
import { comparePassword, hashPassword } from '@/lib/utils/auth'
import { ConflictError } from '@/lib/domain/errors'

/**
 * Student repository implementation
 */
export class StudentRepository {
  constructor(private cache: CacheService) {}

  /**
   * Find all students
   */
  async findAll(): Promise<Student[]> {
    const studentsWithClasses = await db
      .select({
        user: schema.users,
        className: schema.classes.name,
      })
      .from(schema.users)
      .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(eq(schema.users.role, 'student'))

    return studentsWithClasses.map(result => {
      const student = this.mapToStudent(result.user)
      student.className = result.className || undefined
      return student
    })
  }

  /**
   * Find a student by ID
   */
  async findById(id: number): Promise<Student | null> {
    // Try to get from cache first
    const cacheKey = `student:id:${id}`
    const cachedData = await this.cache.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData)
    }

    // If not in cache, get from database
    const [result] = await db
      .select({
        user: schema.users,
        className: schema.classes.name,
      })
      .from(schema.users)
      .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(eq(schema.users.id, id))
      .limit(1)

    if (!result) {
      return null
    }

    const student = this.mapToStudent(result.user)
    student.className = result.className || undefined

    // Cache the result for 5 minutes
    await this.cache.set(cacheKey, JSON.stringify(student), 5 * 60)

    return student
  }

  /**
   * Find a student by unique code
   */
  async findByUniqueCode(uniqueCode: string): Promise<Student | null> {
    // Try to get from cache first
    const cacheKey = `student:profile:${uniqueCode}`
    const cachedData = await this.cache.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData)
    }

    // If not in cache, get from database
    const [result] = await db
      .select({
        user: schema.users,
        className: schema.classes.name,
      })
      .from(schema.users)
      .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(and(eq(schema.users.uniqueCode, uniqueCode), eq(schema.users.role, 'student')))
      .limit(1)

    if (!result) {
      return null
    }

    const student = this.mapToStudent(result.user)
    student.className = result.className || undefined

    // Cache the result for 5 minutes
    await this.cache.set(cacheKey, JSON.stringify(student), 5 * 60)

    return student
  }

  /**
   * Find a student by username
   */
  async findByUsername(username: string): Promise<Student | null> {
    // Try to get from cache first
    const cacheKey = `student:username:${username}`
    const cachedData = await this.cache.get(cacheKey)

    if (cachedData) {
      return JSON.parse(cachedData)
    }

    // If not in cache, get from database
    const [result] = await db
      .select({
        user: schema.users,
        className: schema.classes.name,
      })
      .from(schema.users)
      .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(and(eq(schema.users.username, username), eq(schema.users.role, 'student')))
      .limit(1)

    if (!result) {
      return null
    }

    const student = this.mapToStudent(result.user)
    student.className = result.className || undefined

    // Cache the result for 5 minutes
    await this.cache.set(cacheKey, JSON.stringify(student), 5 * 60)

    return student
  }

  /**
   * Find a student by WhatsApp number
   */
  async findByWhatsApp(whatsapp: string): Promise<Student | null> {
    const [result] = await db
      .select({
        user: schema.users,
        className: schema.classes.name,
      })
      .from(schema.users)
      .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(and(eq(schema.users.whatsapp, whatsapp), eq(schema.users.role, 'student')))
      .limit(1)

    if (!result) {
      return null
    }

    const student = this.mapToStudent(result.user)
    student.className = result.className || undefined
    return student
  }

  /**
   * Find a student by Google email
   */
  async findByGoogleEmail(googleEmail: string): Promise<Student | null> {
    const [result] = await db
      .select({
        user: schema.users,
        className: schema.classes.name,
      })
      .from(schema.users)
      .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
      .where(eq(schema.users.googleEmail, googleEmail))
      .limit(1)

    if (!result) {
      return null
    }

    const student = this.mapToStudent(result.user)
    student.className = result.className || undefined
    return student
  }

  /**
   * Create a new student
   */
  async create(
    studentData: Omit<Student, 'id' | 'createdAt' | 'updatedAt' | 'className'>
  ): Promise<Student> {
    // Validasi: pastikan semua field yang dibutuhkan oleh constraint chk_role_data tersedia
    if (!studentData.uniqueCode || !studentData.username || !studentData.passwordHash) {
      console.error('Missing required fields for student creation:', {
        uniqueCode: !!studentData.uniqueCode,
        username: !!studentData.username,
        passwordHash: !!studentData.passwordHash,
      })
      throw new Error('Required fields missing for student creation')
    }

    // Sesuai migrasi baru, googleEmail sekarang opsional untuk student
    // Tidak perlu lagi membuat placeholder untuk googleEmail

    try {
      // All data including uniqueCode, role, username, passwordHash is now provided by the use case
      const [student] = await db
        .insert(schema.users)
        .values({
          role: studentData.role, // Should be 'student'
          uniqueCode: studentData.uniqueCode,
          googleEmail: studentData.googleEmail || null, // Kini opsional, NULL jika tidak diberikan
          name: studentData.name,
          nis: studentData.nis || null, // Explicit null untuk optional fields
          whatsapp: studentData.whatsapp || null, // Explicit null untuk optional fields
          classId: studentData.classId || null, // Explicit null untuk optional fields
          username: studentData.username, // Always set from studentData
          passwordHash: studentData.passwordHash, // Always set from studentData
          createdAt: new Date(),
        })
        .returning()

      // Invalidate user cache
      await this.cache.del('users:all')

      // Map database result to domain entity
      return this.mapToStudent(student)
    } catch (error) {
      console.error('Failed to create student:', error)
      throw error
    }
  }

  /**
   * Update a student's WhatsApp number
   */
  async updateWhatsApp(id: number, whatsapp: string): Promise<Student> {
    try {
      // Check if WhatsApp number is already in use by another student
      const existingStudent = await this.findByWhatsApp(whatsapp)
      if (existingStudent && existingStudent.id !== id) {
        throw new ConflictError('WhatsApp number already registered to another account')
      }

      // Get the student before updating to access the uniqueCode and username
      const [existingData] = await db
        .select()
        .from(schema.users)
        .where(eq(schema.users.id, id))
        .limit(1)

      if (!existingData) {
        throw new Error(`Student with ID ${id} not found`)
      }

      // Update the student record
      const [student] = await db
        .update(schema.users)
        .set({ whatsapp, updatedAt: new Date() })
        .where(eq(schema.users.id, id))
        .returning()

      // Properly invalidate ALL possible cache keys for this student

      const cachesToInvalidate = [
        `student:id:${id}`,
        `student:profile:${student.uniqueCode}`,
        `student:username:${student.username}`,
        // If there was a previous WhatsApp number, it might be cached as well
        ...(existingData.whatsapp ? [`student:whatsapp:${existingData.whatsapp}`] : []),
        // The new WhatsApp number might have been cached for availability checks
        `student:whatsapp:${whatsapp}`,
      ]

      // Log the keys that will be invalidated

      // Delete all these cache keys
      await Promise.all(cachesToInvalidate.map(key => this.cache.del(key)))

      // Additionally, invalidate the users list cache
      await this.cache.del('users:all')

      // Get the updated student with class information to return
      const [result] = await db
        .select({
          user: schema.users,
          className: schema.classes.name,
        })
        .from(schema.users)
        .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
        .where(eq(schema.users.id, id))
        .limit(1)

      if (!result) {
        // Fallback to the basic student data if the join fails
        return this.mapToStudent(student)
      }

      const updatedStudent = this.mapToStudent(result.user)
      updatedStudent.className = result.className || undefined
      return updatedStudent
    } catch (error) {
      console.error('Error updating WhatsApp number:', error)

      // Check for database constraint errors
      if (
        error instanceof Error &&
        (error.message.includes('unique constraint') ||
          error.message.includes('unique_student_whatsapp') ||
          error.message.includes('duplicate key value'))
      ) {
        throw new ConflictError('WhatsApp number already registered to another account')
      }

      // Re-throw other errors
      throw error
    }
  }

  /**
   * Update a student's NIS
   */
  async updateNIS(id: number, nis: string): Promise<Student> {
    const [student] = await db
      .update(schema.users)
      .set({ nis, updatedAt: new Date() })
      .where(eq(schema.users.id, id))
      .returning()

    // Invalidate cache
    await this.cache.del(`student:profile:${student.uniqueCode}`)

    return this.mapToStudent(student)
  }

  /**
   * Update a student
   */
  async update(id: number, data: UpdateStudentDTO): Promise<Student> {
    try {
      // Create a clean update object with only defined values
      const updateData: Record<string, any> = { updatedAt: new Date() }

      // Only include properties that are explicitly set (not undefined)
      // This allows null values to be set (to clear fields) but ignores undefined
      if (data.name !== undefined) updateData.name = data.name
      if (data.nis !== undefined) updateData.nis = data.nis
      if (data.whatsapp !== undefined) updateData.whatsapp = data.whatsapp
      if (data.googleEmail !== undefined) updateData.googleEmail = data.googleEmail
      if (data.classId !== undefined) {
        // Ensure classId is either a valid number or null
        updateData.classId = data.classId === null ? null : Number(data.classId)

        // If classId is provided but not null, validate it exists
        if (updateData.classId !== null) {
          // Validation can be done here or in the use case
        }
      }

      // Log the update data for debugging
      console.log(`Updating student ${id} with data:`, updateData)

      const [updatedUser] = await db
        .update(schema.users)
        .set(updateData)
        .where(eq(schema.users.id, id))
        .returning()

      if (!updatedUser) {
        throw new Error(`Student with ID ${id} not found`)
      }

      // Invalidate cache
      await this.cache.del(`student:profile:${updatedUser.uniqueCode}`)
      await this.cache.del(`student:id:${id}`)

      // Get the updated student with class information
      const [result] = await db
        .select({
          user: schema.users,
          className: schema.classes.name,
        })
        .from(schema.users)
        .leftJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
        .where(eq(schema.users.id, id))
        .limit(1)

      if (!result) {
        // Fallback in case of error
        return this.mapToStudent(updatedUser)
      }

      const student = this.mapToStudent(result.user)
      student.className = result.className || undefined
      return student
    } catch (error) {
      console.error(`Error updating student ${id}:`, error)
      throw error
    }
  }

  /**
   * Delete a student
   */
  async delete(id: number): Promise<void> {
    const [student] = await db.select().from(schema.users).where(eq(schema.users.id, id)).limit(1)

    if (student) {
      await db.delete(schema.users).where(eq(schema.users.id, id))

      // Invalidate cache
      await this.cache.del(`student:profile:${student.uniqueCode}`)
      await this.cache.del(`student:id:${id}`)
    }
  }

  /**
   * Get the count of students, optionally filtered by class
   */
  async getCount(className?: string): Promise<number> {
    // Create a cacheKey based on the optional class filter
    const cacheKey = `student:count:${className || 'all'}`

    // Try to get from cache first
    const cachedCount = await this.cache.get(cacheKey)

    if (cachedCount) {
      return parseInt(cachedCount, 10)
    }

    // Build the query based on whether we have a class filter
    let result

    // If className is provided, join with classes and filter
    if (className) {
      result = await db
        .select({ count: count() })
        .from(schema.users)
        .innerJoin(schema.classes, eq(schema.users.classId, schema.classes.id))
        .where(and(eq(schema.users.role, 'student'), eq(schema.classes.name, className)))
    } else {
      result = await db
        .select({ count: count() })
        .from(schema.users)
        .where(eq(schema.users.role, 'student'))
    }

    const countValue = result[0]?.count || 0

    // Cache the result for 5 minutes
    await this.cache.set(cacheKey, countValue.toString(), 5 * 60)

    return countValue
  }

  /**
   * Verify a student's password
   */
  async verifyPassword(student: Student, password: string): Promise<boolean> {
    if (!student.passwordHash) {
      return false
    }

    return comparePassword(password, student.passwordHash)
  }

  /**
   * Update a student's credentials (username and/or password)
   */
  async updateCredentials(
    id: number,
    username?: string | null,
    newPassword?: string | null
  ): Promise<Student> {
    // Create a clean update object with only defined values
    const updateData: Record<string, any> = { updatedAt: new Date() }

    // IMPORTANT: For students, username must not be null due to the chk_role_data constraint
    // So we only update username if it's provided and not null
    if (username !== undefined && username !== null) {
      updateData.username = username
    }

    if (newPassword !== undefined && newPassword !== null) {
      updateData.passwordHash = await hashPassword(newPassword)
    }

    const [updatedUser] = await db
      .update(schema.users)
      .set(updateData)
      .where(eq(schema.users.id, id))
      .returning()

    if (!updatedUser) {
      throw new Error(`Student with ID ${id} not found`)
    }

    // Invalidate cache
    if (updatedUser.uniqueCode) {
      await this.cache.del(`student:profile:${updatedUser.uniqueCode}`)
    }
    if (updatedUser.username) {
      await this.cache.del(`student:username:${updatedUser.username}`)
    }
    await this.cache.del(`student:id:${id}`)

    // Get the updated student with class information
    return this.findById(id) as Promise<Student>
  }

  /**
   * Map a database user to a Student entity
   */
  private mapToStudent(user: any): Student {
    return {
      id: user.id,
      uniqueCode: user.uniqueCode,
      googleEmail: user.googleEmail,
      nis: user.nis,
      name: user.name,
      whatsapp: user.whatsapp,
      classId: user.classId,
      className: undefined, // Will be set by caller if needed
      username: user.username, // Ensure always present
      passwordHash: user.passwordHash, // Ensure always present
      role: 'student',
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
  }

  /**
   * Check if a student has attendance records
   */
  async hasAttendanceRecords(uniqueCode: string): Promise<boolean> {
    try {
      // Query to check if there are any attendance records for the student
      const result = await db
        .select({ count: count() })
        .from(schema.absences)
        .where(eq(schema.absences.uniqueCode, uniqueCode))
        .limit(1)

      return result[0]?.count > 0
    } catch (error) {
      console.error(`Error checking if student ${uniqueCode} has attendance records:`, error)
      // In case of an error, assume there are records to be safe
      return true
    }
  }
}
