'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'
import { User, Lock } from 'lucide-react'

export function StudentLoginForm() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [loginError, setLoginError] = useState('')
  const { toast } = useToast()
  const router = useRouter()

  // Check if already authenticated on component mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const res = await fetch('/api/student/check-auth', {
          credentials: 'include',
        })
        if (res.ok) {
          // Already logged in, redirect to home
          router.push('/student/home')
        }
      } catch (err) {
        // Not authenticated, continue showing login form
        
      }
    }

    checkAuth()
  }, [router])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setLoginError('')

    try {
      const response = await fetch('/api/auth/student/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
        credentials: 'include', // Important: include cookies
      })

      const data = await response.json()

      if (response.ok) {
        toast({
          title: 'Login berhasil',
          description: 'Selamat datang kembali!',
        })

        // Add a small delay to ensure cookies are set before redirect
        setTimeout(() => {
          router.push('/student/home')
        }, 500)
      } else {
        setLoginError(data.error || 'Username atau password salah')
        toast({
          title: 'Login gagal',
          description: data.error || 'Username atau password salah',
          variant: 'destructive',
        })
      }
    } catch (error) {
      console.error('Login error:', error)
      setLoginError('Terjadi kesalahan saat login')
      toast({
        title: 'Login gagal',
        description: 'Terjadi kesalahan saat login',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleLogin} className="space-y-4">
      {loginError && (
        <div className="rounded-md bg-red-50 p-2 text-sm text-red-800 dark:bg-red-900/30 dark:text-red-200">
          {loginError}
        </div>
      )}
      <div className="space-y-2">
        <Label htmlFor="username">Username</Label>
        <div className="relative">
          <User className="absolute left-3 top-3 h-4 w-4 text-slate-500" />
          <Input
            id="username"
            type="text"
            placeholder="Masukkan username"
            value={username}
            onChange={e => setUsername(e.target.value)}
            required
            className="pl-10"
            autoComplete="username"
            aria-label="Username"
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <div className="relative">
          <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-500" />
          <Input
            id="password"
            type="password"
            placeholder="Masukkan password"
            value={password}
            onChange={e => setPassword(e.target.value)}
            required
            className="pl-10"
            autoComplete="current-password"
            aria-label="Password"
          />
        </div>
      </div>
      <Button
        type="submit"
        className="w-full bg-indigo-600 hover:bg-indigo-700"
        disabled={loading}
        aria-disabled={loading}
      >
        {loading ? 'Memproses...' : 'Login'}
      </Button>
    </form>
  )
}
