"use client"

import type { ReactNode } from "react"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { ThemeToggle } from "@/components/theme-toggle"
import { StudentBottomNav } from "@/components/student-bottom-nav"

interface StudentLayoutProps {
  children: ReactNode
}

export function StudentLayout({ children }: StudentLayoutProps) {
  const pathname = usePathname()
  // Tidak perlu state untuk menu mobile lagi

  const navItems = [
    { label: "Home", href: "/student/home" },
    // { label: "Riwayat", href: "/student/history" },
    { label: "Profil", href: "/student/profile" },
  ]

  // Determine active tab for mobile bottom nav
  const getActiveTab = () => {
    if (pathname === "/student/home") return "home"
    if (pathname === "/student/history") return "history"
    if (pathname === "/student/profile") return "profile"
    return "home"
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Header with navigation - tampil di semua ukuran layar */}
      <header className="sticky top-0 z-30 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 hidden md:block">
        <div className="container mx-auto px-4">
          <div className="h-16 flex justify-between items-center">
            <div className="flex items-center">
              {/* Mobile menu toggle */}
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">ShalatYuk</h1>
            </div>

            {/* Desktop navigation */}
            <nav className="hidden md:flex items-center space-x-6">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-3 py-2 transition-colors ${
                    pathname === item.href
                      ? "text-indigo-600 dark:text-indigo-400 font-medium"
                      : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </nav>

            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-4 pb-16 md:pb-6">{children}</main>

      {/* Bottom navigation - hanya tampil di mobile */}
      <div className="md:hidden">
        <StudentBottomNav activeTab={getActiveTab() as any} />
      </div>
    </div>
  )
}
