'use client'

import { type ReactNode, useState, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import Link from 'next/link'
import { ThemeToggle } from '@/components/theme-toggle'
import { AdminBottomNav } from '@/components/admin-bottom-nav'
import { Button } from '@/components/ui/button'
import { useAdminSession } from '@/hooks/use-admin-session'
import { Camera, FileText, Users, User, Menu, X, LogOut, GraduationCap, Shield } from 'lucide-react'
import { Skeleton } from '@/components/ui/skeleton'

interface AdminLayoutProps {
  children: ReactNode
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { admin, loading, logout } = useAdminSession()

  // Redirect ke halaman login jika tidak ada sesi admin
  useEffect(() => {
    if (!loading && !admin) {
      router.push('/admin')
    }
  }, [admin, loading, router])

  // Check if admin is super_admin
  const isSuperAdmin = admin?.role === 'super_admin'

  // Define navigation items based on admin role
  const navItems = [
    { icon: <Camera className="h-5 w-5" />, label: 'Scanner', href: '/admin/home' },
    { icon: <FileText className="h-5 w-5" />, label: 'Laporan', href: '/admin/reports' },
  ]

  // Add Users, Admins, Classes and Sessions tabs only for super_admin
  if (isSuperAdmin) {
    navItems.push(
      { icon: <Users className="h-5 w-5" />, label: 'Siswa', href: '/admin/users' },
      { icon: <Users className="h-5 w-5" />, label: 'Admin', href: '/admin/admins' },
      { icon: <GraduationCap className="h-5 w-5" />, label: 'Kelas', href: '/admin/classes' },
      { icon: <Shield className="h-5 w-5" />, label: 'Sesi', href: '/admin/sessions' }
    )
  }

  // Add Profile tab for all admins
  navItems.push({ icon: <User className="h-5 w-5" />, label: 'Profil', href: '/admin/profile' })

  // Determine active tab for mobile bottom nav
  const getActiveTab = () => {
    if (pathname === '/admin/home') return 'home'
    // if (pathname === "/admin/dashboard") return "dashboard"
    if (pathname === '/admin/reports') return 'reports'
    if (pathname === '/admin/users') return 'users'
    if (pathname === '/admin/admins') return 'admins'
    if (pathname === '/admin/classes') return 'classes'
    if (pathname === '/admin/sessions') return 'sessions'
    if (pathname === '/admin/profile') return 'profile'
    return 'home'
  }

  // Tampilkan loading state jika sedang memeriksa sesi
  if (loading) {
    return (
      <div className="flex min-h-screen flex-col bg-slate-50 p-4 dark:bg-slate-900">
        <div className="mb-6 flex items-center justify-between">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
        <div className="flex flex-1">
          <div className="mr-6 hidden w-64 md:block">
            <Skeleton className="mb-2 h-10 w-full" />
            <Skeleton className="mb-2 h-10 w-full" />
            <Skeleton className="mb-2 h-10 w-full" />
            <Skeleton className="mb-2 h-10 w-full" />
          </div>
          <div className="flex-1">
            <Skeleton className="mb-4 h-32 w-full" />
            <Skeleton className="mb-4 h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    )
  }

  // Jika tidak ada admin, jangan render apa-apa (akan di-redirect)
  if (!admin) {
    return null
  }

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Header - tampil di semua ukuran layar */}
      <header className="sticky top-0 z-30 hidden border-b border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-800 md:block">
        <div className="flex h-16 items-center justify-between px-4">
          <div className="flex items-center">
            {/* Sidebar toggle untuk mobile */}
            <Button
              variant="ghost"
              size="icon"
              className="mr-2 md:hidden"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              aria-label="Toggle menu"
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
            <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">ShalatYuk</h1>
          </div>

          <div className="flex items-center gap-4">
            <span className="hidden text-sm text-slate-500 dark:text-slate-400 md:inline-block">
              Admin: {admin.name}
            </span>
            <ThemeToggle />
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-64px)]">
        {/* Mobile sidebar - overlay when open */}
        <div
          className={`fixed inset-0 z-20 bg-black/50 transition-opacity md:hidden ${
            sidebarOpen ? 'opacity-100' : 'pointer-events-none opacity-0'
          }`}
          onClick={() => setSidebarOpen(false)}
        />

        {/* Sidebar - fixed on mobile, permanent on desktop */}
        <div
          className={`fixed z-20 h-full w-64 border-r border-slate-200 bg-white transition-transform duration-300 ease-in-out dark:border-slate-700 dark:bg-slate-800 md:static ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
          }`}
        >
          <div className="flex h-full flex-col">
            <div className="border-b border-slate-200 p-4 dark:border-slate-700 md:hidden">
              <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100">Menu</h2>
            </div>

            <div className="flex-1 overflow-auto p-4">
              <nav className="space-y-1">
                {navItems.map(item => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center gap-3 rounded-md px-3 py-2 transition-colors ${
                      pathname === item.href
                        ? 'bg-indigo-50 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400'
                        : 'text-slate-600 hover:bg-slate-100 dark:text-slate-400 dark:hover:bg-slate-700'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                ))}
              </nav>
            </div>

            <div className="border-t border-slate-200 p-4 dark:border-slate-700">
              <div className="flex flex-col space-y-2">
                <div className="flex items-center gap-3 text-sm text-slate-500 dark:text-slate-400">
                  <User className="h-4 w-4" />
                  <span>{admin.name}</span>
                </div>
                <button
                  onClick={logout}
                  className="flex items-center gap-2 text-sm text-red-500 transition-colors hover:text-red-600"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 overflow-auto">
          <main className="p-4">{children}</main>
        </div>
      </div>

      {/* Bottom navigation - hanya tampil di mobile */}
      <div className="md:hidden">
        <AdminBottomNav activeTab={getActiveTab() as any} adminRole={admin.role} />
      </div>
    </div>
  )
}
