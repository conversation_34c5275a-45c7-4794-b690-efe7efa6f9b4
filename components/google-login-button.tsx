'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useToast } from '@/components/ui/use-toast'
import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { signIn } from 'next-auth/react'

// Loading state component
function LoadingButton() {
  return (
    <Button
      className="flex w-full items-center justify-center bg-indigo-600 text-white hover:bg-indigo-700"
      disabled
    >
      <svg
        className="mr-2 h-4 w-4 animate-spin"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
          fill="none"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      Loading...
    </Button>
  )
}

// Main button component that uses useSearchParams
function GoogleLoginButtonContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)

  // Check for auto_login parameter to handle automatic login attempts
  useEffect(() => {
    const autoLogin = searchParams.get('auto_login')
    if (autoLogin === 'true') {
      
      // Short delay to ensure page is fully loaded
      const timer = setTimeout(() => {
        handleLogin()
        // Remove the auto_login parameter from URL
        window.history.replaceState({}, document.title, window.location.pathname)
      }, 500)
      return () => clearTimeout(timer)
    }
  }, [searchParams])

  // Check for error parameters in URL after redirect from OAuth flow
  useEffect(() => {
    const error = searchParams.get('error')
    if (error) {
      let errorMessage = 'Login gagal, silakan coba lagi.'

      switch (error) {
        case 'google_auth_failed':
        case 'Callback':
          errorMessage = 'Autentikasi Google gagal.'
          break
        case 'OAuthSignin':
          errorMessage = 'Gagal memulai proses login Google.'
          break
        case 'OAuthCallback':
          errorMessage = 'Gagal menyelesaikan autentikasi Google.'
          break
        case 'AccessDenied':
          errorMessage = 'Akses ditolak oleh sistem.'
          break
        case 'auth':
        default:
          errorMessage = 'Terjadi kesalahan saat login. Silakan coba lagi.'
          break
      }

      toast({
        title: 'Login Gagal',
        description: errorMessage,
        variant: 'destructive',
      })

      // Clear the error from URL
      window.history.replaceState({}, document.title, window.location.pathname)
    }
  }, [searchParams, toast])

  const handleLogin = async () => {
    try {
      setLoading(true)

      // First try to logout any existing session
      try {
        await fetch('/api/auth/logout', { method: 'POST' })
        
      } catch (logoutError) {
        
      }

      // Use NextAuth's signIn function to start OAuth flow
      // Force the redirect URL to be absolute to avoid any issues
      const callbackUrl = `${window.location.origin}/student/home`
      

      await signIn('google', {
        callbackUrl,
        redirect: true,
      })
    } catch (error) {
      setLoading(false)
      console.error('Login error:', error)
      toast({
        title: 'Login gagal',
        description: 'Tidak dapat terhubung ke Google',
        variant: 'destructive',
      })
    }
  }

  return (
    <Button
      onClick={handleLogin}
      className="flex w-full items-center justify-center bg-indigo-600 text-white hover:bg-indigo-700"
      disabled={loading}
    >
      {/* Google Icon */}
      <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          fill="#4285F4"
        />
        <path
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          fill="#34A853"
        />
        <path
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          fill="#FBBC05"
        />
        <path
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          fill="#EA4335"
        />
        <path d="M1 1h22v22H1z" fill="none" />
      </svg>
      {loading ? 'Memproses...' : 'Login with Google'}
    </Button>
  )
}

// Export the wrapped component with Suspense
export function GoogleLoginButton() {
  return (
    <Suspense fallback={<LoadingButton />}>
      <GoogleLoginButtonContent />
    </Suspense>
  )
}
