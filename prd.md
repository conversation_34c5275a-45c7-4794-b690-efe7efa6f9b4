# Product Requirements Document (PRD)

## Prayer Attendance Application for Banjarmasin High School Students

---

## 1. Background and Objectives

**Background:**
A high school in Banjarmasin aims to automate attendance tracking for Zuhr, Asr prayers, and student dismissal using a web-based application. The current manual process is inefficient and lacks real-time reporting. With a limited budget, the solution leverages Next.js with API Routes for frontend and backend, Drizzle ORM for PostgreSQL, Redis for caching, and n8n Webhook for WhatsApp OTP, hosted on a VPS with EasyPanel. The application adheres to Clean Architecture and programming best practices for clean, scalable, maintainable, and resource-efficient code.

**Objectives:**

- Develop a production-ready web application with a Landing Page, Student App, and Admin App using Next.js (App Router and API Routes) with Clean Architecture.
- Design an informative Landing Page without application access for security, based on modern UI principles.
- Implement the UI using Tailwind CSS, shadcn/ui, and next-themes for light/dark mode, delivering an elegant, reusable, and responsive design.
- Build the backend with Next.js API Routes, Drizzle ORM for PostgreSQL, Redis for caching, and n8n Webhook for WhatsApp OTP.
- For student login use username/password authentication.
- Implement password reset functionality at page reset password via simple step just need old password and new password.
- Implement forget password with sent request to whatsapp and after that input new password
- Generate unique codes for QR code scanning instead of NIS, with optional NIS input in the profile.
- Ensure security (JWT, rate limiting, input validation with Zod), cost-effective scalability, and easy management on EasyPanel.
- Apply Clean Architecture with Presentation, Domain, and Data layers for maintainability.
- Follow programming best practices (clean code, type safety, testing, modularity, logging) for high-quality code.
- Deliver an award-worthy design inspired by Notion, Figma, and Stripe, aligned with Nielsen Norman Group usability principles.
- Support WITA (UTC+8) time for Banjarmasin context.

---

## 2. Stakeholders

- **Students:** Use the Student App to log in with username and password, complete their profile (WhatsApp number), display QR codes with unique codes, and manage profiles.
- **Admins (Teachers/Staff):** Use the Admin App to scan QR codes, manage users, and view reports.
- **School:** Requires a cost-effective, efficient attendance solution.
- **Developers:** Team building the application with Next.js, Drizzle ORM, and EasyPanel.
- **VPS Admin:** Manages EasyPanel, PostgreSQL, Redis, and n8n on the VPS.

---

## 3. Project Scope

### In Scope

- Web application: Landing Page, Student App, Admin App.
- Informative Landing Page without links to /student or /admin.
- Student App: Username and password login, WhatsApp OTP verification for profile, Home (QR code with unique code), Profile (WhatsApp number, optional NIS, optional Google email, reset password).
- Admin App: Login, Home (Scanner), Reports, User Management, Profile.
- Backend: Next.js API Routes with Clean Architecture for authentication, attendance, reports, and user management.
- Database: PostgreSQL on EasyPanel, accessed via Drizzle ORM.
- Caching: Redis on EasyPanel for profiles, reports, JWT sessions, and OTPs.
- Integration: n8n Webhook on EasyPanel for WhatsApp OTP.
- Security: JWT, rate limiting, input validation with Zod, HTTPS.
- Deployment: Docker on EasyPanel for the Next.js app.
- Monitoring: Logging with `console.log`, VPS metrics via EasyPanel, optional Sentry if budget allows.
- Frontend: Next.js (App Router), Tailwind CSS, shadcn/ui, next-themes, framer-motion.
- Best Practices: Clean code, type safety, unit testing (Jest), component testing (React Testing Library), linting (ESLint), formatting (Prettier).
- Responsive: Mobile (min-width 320px) to desktop (max-width 1280px).
- Accessibility: WCAG 2.1 Level AA.
- Time: WITA (UTC+8) for all timestamps.

### Out of Scope

- Native mobile application.
- Integration with other school systems (e.g., SIS).
- Features beyond Zuhr, Asr, and dismissal attendance.
- External cloud services (e.g., AWS, Twilio).

---

## 4. Key Features

### 4.1 Landing Page (Informative)

**Purpose:** Introduce the application without providing access to Student or Admin Apps for security.

**Features:**

- Hero Section: Headline "Modern Prayer Attendance for Future Education", subheadline on efficiency, religious image (mosque/students praying, rounded-lg, border indigo-600).
- About Section: Description of the application in a shadcn/ui Card.
- Features Section: Three shadcn/ui Cards for QR Code, Scanner, and Reports, each with Lucide icons (QrCodeIcon, CameraIcon, FileTextIcon).
- Footer: Text "Banjarmasin High School - 2025 | Contact: +6281234567890".
- Theme Toggle: shadcn/ui Button with SunIcon/MoonIcon for light/dark mode.

**Security:** No buttons/links to /student or /admin.

**Interactions:** Theme toggle, fade-in/scale-in animations with framer-motion on load.

### 4.2 Student App

#### Login

- **UI:** Title "Masuk ke Akun Siswa", username/password fields with "Forgot Password" link.
- **API:**
  - `POST /api/auth/student/login`: Handles username/password login, verifies credentials, returns JWT and refresh token.
- **Interactions:** Login with username and password, click button login, redirects to Home. Client-side Toast "Login successful" on success.
- **Security:** JWT with password hashing for secure authentication.

#### Password Reset

- **UI:** Forgot Password page with tabs for Email and WhatsApp recovery options, OTP input (for WhatsApp), new password input.
- **API:**
  - `POST /api/auth/password-reset/request`: Sends password reset OTP via WhatsApp or email link.
  - `POST /api/auth/password-reset/verify`: Verifies OTP or token, updates password.
- **Interactions:** Toast notifications for successful OTP/link sending and password reset. Redirects to login page after successful reset.
- **Security:** OTP stored in Redis with 5-minute TTL, email tokens with 60-minute TTL.

#### WhatsApp OTP Verification (Profile)

- **UI:** Prompt in Profile for WhatsApp number if missing, shadcn/ui Input with PhoneIcon, shadcn/ui Button "Kirim OTP" (purple). OTP input field (6 digits), shadcn/ui Button "Verifikasi OTP" (purple), "Kembali" (secondary).
- **API:**
  - `POST /api/student/whatsapp/send-otp`: Sends OTP via n8n Webhook, stores OTP in Redis (5-minute TTL).
  - `POST /api/student/whatsapp/verify-otp`: Verifies OTP, updates WhatsApp number in database, invalidates profile cache.
- **Interactions:** Toast "OTP sent to your WhatsApp" after sending, "WhatsApp number verified" or "Invalid OTP" after verification. Redirects to Home after successful verification.

#### Home

- **UI:** Title "QR Code Absensi", displays unique code (UUID), QR code (encodes unique code using qrcode.react), status section for Zuhr, Asr, Pulang with checkmarks/crosses, date/time (WITA, format DD/MM/YYYY HH:mm).
- **API:** `GET /api/student/profile` (fetches profile including unique code, cached in Redis with 5-minute TTL).
- **Interactions:** QR code refreshes every 5 minutes (client-side), status updates real-time via API polling, shadcn/ui Tabs for navigation (HomeIcon active, UserIcon for Profile).

#### Profile

- **UI:** Title "Profil Siswa", fields for Name (read-only), WhatsApp Number (editable, requires OTP verification), optional NIS (editable, shadcn/ui Input), optional Google Email (editable, opsional, tidak digunakan untuk login), Class (read-only, set by admin). shadcn/ui Button "Simpan" (purple), "Logout" (red).
- **API:**
  - `PATCH /api/student/profile`: Updates WhatsApp number (post-OTP verification), optional NIS, optional Google email, invalidates Redis cache.
  - `POST /api/auth/logout`: Removes refresh token from Redis, clears client-side session.
- **Interactions:** Client-side form validation with Zod, Toast for feedback ("Profile updated", "Logout successful"). Prompt to add WhatsApp number if missing (Dialog with input and OTP flow).

### 4.3 Admin App

#### Login

- **UI:** Title "Masuk ke Akun Admin", fields for Username and Password, shadcn/ui Input with icons (UserIcon, LockIcon), shadcn/ui Button "Masuk" (purple).
- **API:** `POST /api/auth/admin/login` (returns JWT and refresh token, caches in Redis).
- **Interactions:** Client-side validation with Zod, Toast "Login successful", navigates to Home.

#### Home (Scanner)

- **UI:** Title "Scanner Admin", shadcn/ui Select for attendance type (Zuhr, Asr, Pulang), shadcn/ui Button "Mulai Pemindaian" (purple), QR scanner (jsQR), shadcn/ui Card for scan result (name, class, unique code), shadcn/ui Dialog for duplicate attendance, shadcn/ui Tabs for navigation (CameraIcon active).
- **API:**
  - `POST /api/absence/record`: Records attendance (unique code, type, WITA timestamp).
  - `GET /api/absence/check`: Checks for duplicate attendance (same day, type, unique code, cached in Redis).
  - `GET /api/student/{unique_code}`: Fetches student data by unique code (cached in Redis).
- **Interactions:** Scanner activates camera, Dialog prompts for duplicate attendance ("Record again?" with "Proceed"/"Cancel").

#### Reports

- **UI:** Title "Laporan Absensi", filters for Tanggal and Kelas (shadcn/ui Select), Name/Unique Code search (shadcn/ui Input), shadcn/ui Table with Unique Code, Name, Class, Zuhr, Asr, Pulang, Time (WITA), shadcn/ui Button "Export CSV" (green).
- **Attendance Summary:** Three shadcn/ui Cards showing attendance statistics - Zuhr, Asr, and Dismissal counts with percentages and progress bars in distinct colors (indigo for Zuhr, emerald for Asr, amber for Dismissal).
- **API:** `GET /api/absence/reports` (fetches filtered reports from attendance_summary, cached in Redis with 1-minute TTL).
- **Refresh:** On-demand refresh of attendance_summary materialized view via `POST /api/refresh-summary` (refreshes if data is older than 5 minutes).
- **Interactions:** Real-time filtering, CSV export with filename reports_DD-MM-YYYY.csv.

#### User Management

- **UI:** Title "Manajemen User", shadcn/ui Table with ID, Name, Role, Unique Code (for students), shadcn/ui Button "Tambah User" (purple), edit/delete icons. Dialog for adding/editing users (Role, Name, Class for students, Username/Password for admins).
- **APIs:**
  - `GET /api/users`: Lists users (cached in Redis).
  - `POST /api/users`: Adds user (generates unique code for students).
  - `PATCH /api/users/{id}`: Edits user.
  - `DELETE /api/users/{id}`: Deletes user.
- **Interactions:** Client-side form validation, Toast for feedback.

#### Profile

- **UI:** Title "Profil Admin", fields for Username and Name, shadcn/ui Button "Simpan" (purple), "Logout" (red).
- **API:** `PATCH /api/admin/profile` (updates database).
- **Interactions:** Client-side form validation, Toast for feedback.

---

## 5. Technical Specifications

### 5.1 Technology Stack

- **Frontend and Backend:**

  - Framework: Next.js 14.x (App Router, API Routes)
  - Styling: Tailwind CSS 3.x with shadcn/ui components (Button, Input, Select, Table, Dialog, Toast, Tabs)
  - Theme Management: next-themes for light/dark/system mode, detects prefers-color-scheme, stores in localStorage
  - Animations: Framer-motion 10.x for transitions and microinteractions
  - QR Code: qrcode.react for Student App Home (encodes unique code)
  - Scanner: jsQR for Admin App Scanner
  - Icons: Lucide Icons from shadcn/ui (PhoneIcon, QrCodeIcon, etc.)
  - Authentication:
    - Students: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
    - Admins: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
  - Database: PostgreSQL 15.x on EasyPanel, accessed via Drizzle ORM
  - Caching: Redis 7.x on EasyPanel with @upstash/redis
  - Validation: Zod for input validation in API Routes
  - WhatsApp Integration: n8n Webhook on EasyPanel for OTP
  - Security:
    - Rate limiting with custom middleware
    - HTTPS via EasyPanel (Let's Encrypt)
  - Logging: Standard `console.log`, `console.info`, `console.error` for application events (debug, info, error levels)
  - Error Tracking: Optional Sentry if budget allows
  - Testing: Jest 29.x for unit tests, React Testing Library for component tests
  - Linting/Formatting: ESLint with TypeScript rules
  - Prettier for consistent formatting
  - Husky for pre-commit linting/testing
  - Password Hashing: bcrypt for admin passwords

- **Infrastructure:**
  - VPS: EasyPanel for hosting Next.js app, PostgreSQL, Redis, and n8n
  - Database: PostgreSQL on EasyPanel (port 5432)
  - Caching: Redis on EasyPanel (port 6379)
  - Workflow: n8n on EasyPanel for WhatsApp Webhook
  - Container: Docker for Next.js app
  - HTTPS: Managed by EasyPanel with Let's Encrypt
  - Backup: Daily PostgreSQL backups via EasyPanel

### 5.2 Clean Architecture

- **Layers:**
  - Presentation:
    - Frontend: UI components (pages, components) for rendering and interaction
      - Example: `app/student/home/<USER>
    - Backend: API Routes for HTTP requests/responses
      - Example: `app/api/auth/student/login/route.ts`
  - Domain: Entities and business logic (use cases), framework-agnostic
    - Example: `lib/domain/entities/student.ts`, `lib/domain/usecases/auth.ts`
  - Data: Repositories for database (Drizzle ORM) and caching (Redis) access
    - Example: `lib/data/repositories/student.ts`, `lib/data/cache/redis.ts`
- **Dependency Rule:**

  - Presentation depends on Domain
  - Domain is independent (pure TypeScript)
  - Data depends on Domain for entity definitions

- **Folder Structure:**

```text
app/
  page.tsx # Landing Page
  student/ # Student App routes
    page.tsx # Login
    whatsapp-otp/ # WhatsApp OTP Verification
    home/ # Home (QR Code)
    profile/ # Profile
  admin/ # Admin App routes
    page.tsx # Login
    home/ # Scanner
    reports/ # Reports
    user-management/ # User Management
    profile/ # Profile
api/
  auth/
    student/
      logout/route.ts # POST /api/auth/logout
    admin/
      login/route.ts # POST /api/auth/admin/login
    whatsapp/
      send-otp/route.ts # POST /api/student/whatsapp/send-otp
      verify-otp/route.ts # POST /api/student/whatsapp/verify-otp
    student/
      profile/route.ts # GET, PATCH /api/student/profile
      [unique_code]/route.ts # GET /api/student/{unique_code}
    absence/
      record/route.ts # POST /api/absence/record
      check/route.ts # GET /api/absence/check
      reports/route.ts # GET /api/absence/reports
      refresh-summary/ # POST /api/refresh-summary
    users/
      route.ts # GET, POST /api/users
      [id]/route.ts # PATCH, DELETE /api/users/{id}
    admin/
      profile/route.ts # PATCH /api/admin/profile
components/
  ui/ # shadcn/ui components
  common/ # ThemeToggle, BottomNav
lib/
  domain/
    entities/ # Entities
      student.ts # Student interface
      admin.ts # Admin interface
      absence.ts # Absence interface
    usecases/ # Business logic
      auth.ts # login, OTP
      absence.ts # Record absence, check duplicate
      reports.ts # Generate reports
    errors/ # Custom errors
      index.ts # NotFoundError, ValidationError
  data/
    repositories/ # Database access
      student.ts # Student CRUD
      admin.ts # Admin CRUD
      absence.ts # Absence CRUD
    cache/ # Cache access
      redis.ts # Redis utilities
drizzle/
  schema.ts # Drizzle schema
  db.ts # Database connection
utils/
styles/
  globals.css # Tailwind styles
types/
  index.ts # Shared TypeScript types
tests/
  domain/ # Unit tests for Domain
  components/ # Component tests
  data/ # Repository tests
```

### 5.3 Programming Best Practices

- **Clean Code:**
  - DRY: Reusable components (e.g., shadcn/ui Button)
  - KISS: Simple logic in API Routes, business logic in Domain
  - Single Responsibility: One function/component per task
  - Naming: Descriptive (e.g., generateUniqueCode, verifyWhatsAppOtp)
  - Comments: Explain complex logic (e.g., OTP verification)
- **Type Safety:**
  - TypeScript for all code, integrated with Drizzle ORM schema
  - Zod for API input validation (e.g., WhatsApp number format)
  - JSDoc for complex types
- **Error Handling:**
  - Try-catch in API Routes and repositories
  - Standard error response: `{ error: string, status: number }`
  - Custom errors: NotFoundError, ValidationError in `lib/domain/errors/`
- **Testing:**
  - Unit tests for Domain (e.g., generateUniqueCodeUseCase)
  - Component tests for UI
  - Integration tests for API Routes with mocked database/Redis
  - Coverage: >80% for Domain and Data layers
- **Linting/Formatting:**
  - ESLint with TypeScript rules
  - Prettier for consistent formatting
  - Husky for pre-commit linting/testing
- **Logging:**
  - Standard `console.log`, `console.info`, `console.error` for application events (debug, info, error levels)
  - Example: `console.info("Student registered", { email: "<EMAIL>", uniqueCode: "uuid" })`
- **Performance:**
  - Redis caching for profiles/reports (>80% hit rate)
  - Drizzle ORM with indexed queries
  - Lazy-load components with next/dynamic
  - Optimize images with next/image

### 5.4 Database Schema (PostgreSQL)

```sql
CREATE TYPE user_role AS ENUM ('student', 'admin');
CREATE TYPE attendance_type AS ENUM ('Zuhr', 'Asr', 'Pulang');

CREATE TABLE classes (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  name VARCHAR(10) NOT NULL UNIQUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT chk_class_name CHECK (name ~ '^[XVI]{1,2}\s(IPA|IPS)\s[1-3]$')
);

CREATE TABLE users (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  role user_role NOT NULL,
  unique_code VARCHAR(36) UNIQUE, -- UUID for QR code (students only)
  google_email VARCHAR(255) UNIQUE, -- Google email for students (optional, not used for login)
  nis VARCHAR(10), -- Optional NIS for students
  username VARCHAR(50) UNIQUE, -- For admins and students with password authentication
  name VARCHAR(100) NOT NULL CHECK (name <> ''),
  whatsapp VARCHAR(15), -- Verified via OTP for students
  class_id BIGINT REFERENCES classes(id),
  password_hash VARCHAR(255), -- For admins and students with password authentication
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ,
  CONSTRAINT chk_nis_format CHECK (nis IS NULL OR nis ~ '^[A-Za-z0-9]{1,10}$'),
  CONSTRAINT chk_role_data CHECK (
    (role = 'student' AND unique_code IS NOT NULL AND username IS NOT NULL AND password_hash IS NOT NULL) OR
    (role = 'admin' AND google_email IS NULL AND unique_code IS NULL AND username IS NOT NULL AND password_hash IS NOT NULL)
  )
);

CREATE TABLE absences (
  id BIGINT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  unique_code VARCHAR(36) NOT NULL REFERENCES users(unique_code),
  type attendance_type NOT NULL,
  recorded_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_unique_code FOREIGN KEY (unique_code) REFERENCES users(unique_code) ON DELETE RESTRICT
);

CREATE MATERIALIZED VIEW attendance_summary AS
WITH pivoted AS (
  SELECT
    DATE(recorded_at) AS summary_date,
    unique_code,
    MAX(CASE WHEN type = 'Zuhr' THEN TRUE ELSE FALSE END) AS zuhr,
    MAX(CASE WHEN type = 'Asr' THEN TRUE ELSE FALSE END) AS asr,
    MAX(CASE WHEN type = 'Pulang' THEN TRUE ELSE FALSE END) AS pulang,
    MAX(recorded_at) AS last_updated
  FROM absences
  GROUP BY DATE(recorded_at), unique_code
)
SELECT
  p.summary_date,
  u.unique_code,
  u.name,
  c.name AS class_name,
  p.zuhr,
  p.asr,
  p.pulang,
  p.last_updated AS updated_at
FROM pivoted p
JOIN users u ON p.unique_code = u.unique_code
JOIN classes c ON u.class_id = c.id
WITH NO DATA;

CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_class_id ON users(class_id) WHERE class_id IS NOT NULL;
CREATE INDEX idx_users_unique_code ON users(unique_code) WHERE unique_code IS NOT NULL;
CREATE INDEX idx_absences_unique_code_recorded_at ON absences(unique_code, recorded_at);
CREATE INDEX idx_attendance_summary_date ON attendance_summary(summary_date);
```

### 5.5 Database Integration

- Drizzle ORM: Maps PostgreSQL schema to TypeScript for type-safe queries.
- Unique Code: Generated with uuid library (v4) upon Admin make user, stored in users.unique_code.
- Materialized View: Refreshed on-demand via `POST /api/refresh-summary` if data is older than 5 minutes.
- Performance:
  - Indexes on unique_code, recorded_at for fast queries.
  - Redis caching for reports and profiles.

### 5.6 Username & Password Authentication

- **Authentication for Students:**
  - Username/Password: JWT with bcrypt for password hashing.
  - Forget Password: WhatsApp OTP or email link recovery options.
  - Reset password: Input old password and new password
- **Configuration:**
  - Environment variables: NEXTAUTH_SECRET, JWT_SECRET.
  - Provider setup in `app/api/auth/[...nextauth]/route.ts`.
- **Flow:**
  - Username/Password: User enters credentials → Verifies against database → Issues JWT token.
  - Forget Password: User requests reset → Sends OTP/link → Verifies token → Updates password.
  - Reset Password: User input old password and new password on page reset password
  - Stores refresh token in Redis (TTL: 7 days).

### 5.7 Redis Usage

- Profile Caching: `GET /api/student/profile` (TTL: 5 minutes, key: student:profile:{unique_code})
- Reports Caching: `GET /api/absence/reports` (TTL: 1 minute, key: absence:reports:{date}:{class})
- JWT Sessions: Refresh tokens (TTL: 7 days, key: auth:refresh:{user_id})
- OTP: Temporary OTPs (TTL: 5 minutes, key: otp:{whatsapp})

### 5.8 Security

- **Authentication:**
  - Students: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
  - Admins: Username/password with JWT (jsonwebtoken), refresh tokens in Redis
- **Rate Limiting:** 100 requests/15 minutes/IP
- **Validation:** Zod for WhatsApp number, OTP, and optional NIS
- **HTTPS:** Managed by EasyPanel
- **Data Sanitization:** Drizzle ORM prevents SQL injection

### 5.9 Color Palette, Typography, Animations, Accessibility

- **Color Palette:**
  - Primary: indigo-600 (hover: indigo-700)
  - Neutral: Light (slate-50, white), Dark (slate-900, slate-800)
  - QR Code: White background, black foreground, border indigo-600
- **Typography:** Inter font, sizes from 12px (caption) to 48px (H1)
- **Animations:** Fade-in (pages), scale-in (Dialog), slide-in (Toast)
- **Accessibility:** WCAG 2.1 Level AA, ARIA labels, keyboard navigation

---

## 6. Functional Specifications

### Student App

- **Login:** Login button, redirects to Home
- **WhatsApp OTP:** Input WhatsApp number, send OTP, verify OTP, update profile
- **Home:** Displays unique code, QR code, attendance status
- **Profile:** Edit WhatsApp (with OTP), optional NIS, username, optional email, class, button reset password

### Admin App

- except QR scanner uses unique_code

---

## 7. Non-Functional Requirements

- **Performance:** API < 200ms, page load < 2 seconds, scalable for 1,000 users
- **Security:** JWT, rate limiting, HTTPS
- **Reliability:** 99.9% uptime, error rate < 0.1%
- **Maintainability:** Clean Architecture, >80% test coverage, comprehensive docs

---

## 8. Assumptions and Constraints

- **Assumptions:**
  - username and password credentials are provided by the admin
  - EasyPanel supports n8n Webhook and Redis
- **Constraints:**
  - Limited VPS resources (2GB RAM, 2 CPUs)
  - n8n stability

---

## 9. Success Criteria

- Stable deployment on EasyPanel with 99.9% uptime
- Username and password login (no Google login), WhatsApp OTP verification, and unique code QR scanning
- Elegant, responsive UI with light/dark mode
- Accurate attendance tracking and real-time reports
- Adherence to Clean Architecture and best practices (>80% test coverage)
